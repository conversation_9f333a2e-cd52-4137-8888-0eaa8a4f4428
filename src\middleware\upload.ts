import multer from 'multer';
import { Request } from 'express';

// File type validation
const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
const allowedDocumentTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

// File size limits (in bytes)
const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
const MAX_DOCUMENT_SIZE = 10 * 1024 * 1024; // 10MB

/**
 * File filter function
 */
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const fieldName = file.fieldname;
  
  // Check file type based on field name
  if (fieldName === 'profilePicture' || fieldName === 'eventImage' || fieldName === 'postImage') {
    if (allowedImageTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type for ${fieldName}. Only JPEG, PNG, GIF, and WebP images are allowed.`));
    }
  } else if (fieldName === 'resume') {
    if (allowedDocumentTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type for resume. Only PDF and Word documents are allowed.'));
    }
  } else {
    cb(new Error('Unknown field name'));
  }
};

/**
 * File size limit function
 */
const limits = {
  fileSize: (req: Request, file: Express.Multer.File) => {
    const fieldName = file.fieldname;
    
    if (fieldName === 'profilePicture' || fieldName === 'eventImage' || fieldName === 'postImage') {
      return MAX_IMAGE_SIZE;
    } else if (fieldName === 'resume') {
      return MAX_DOCUMENT_SIZE;
    }
    
    return MAX_IMAGE_SIZE; // Default
  }
};

/**
 * Multer configuration
 */
export const uploadMiddleware = multer({
  storage: multer.memoryStorage(), // Store files in memory for Cloudinary upload
  fileFilter,
  limits: {
    fileSize: MAX_DOCUMENT_SIZE, // Use the larger limit as the general limit
    files: 1, // Only allow one file per request
  },
});

/**
 * Validate file size based on field name
 */
export const validateFileSize = (file: Express.Multer.File): boolean => {
  const fieldName = file.fieldname;
  
  if (fieldName === 'profilePicture' || fieldName === 'eventImage' || fieldName === 'postImage') {
    return file.size <= MAX_IMAGE_SIZE;
  } else if (fieldName === 'resume') {
    return file.size <= MAX_DOCUMENT_SIZE;
  }
  
  return false;
};

/**
 * Get allowed file types for a field
 */
export const getAllowedTypes = (fieldName: string): string[] => {
  if (fieldName === 'profilePicture' || fieldName === 'eventImage' || fieldName === 'postImage') {
    return allowedImageTypes;
  } else if (fieldName === 'resume') {
    return allowedDocumentTypes;
  }
  
  return [];
};

/**
 * Get max file size for a field
 */
export const getMaxFileSize = (fieldName: string): number => {
  if (fieldName === 'profilePicture' || fieldName === 'eventImage' || fieldName === 'postImage') {
    return MAX_IMAGE_SIZE;
  } else if (fieldName === 'resume') {
    return MAX_DOCUMENT_SIZE;
  }
  
  return MAX_IMAGE_SIZE;
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Generate unique filename
 */
export const generateFileName = (originalName: string, userId: string, fieldName: string): string => {
  const timestamp = Date.now();
  const extension = originalName.split('.').pop();
  return `${fieldName}_${userId}_${timestamp}.${extension}`;
};

/**
 * Validate image dimensions (optional - for future use)
 */
export const validateImageDimensions = (width: number, height: number, fieldName: string): boolean => {
  if (fieldName === 'profilePicture') {
    // Profile pictures should be square-ish and reasonable size
    return width >= 100 && height >= 100 && width <= 2000 && height <= 2000;
  } else if (fieldName === 'eventImage' || fieldName === 'postImage') {
    // Event/post images can be more flexible
    return width >= 200 && height >= 200 && width <= 4000 && height <= 4000;
  }
  
  return true;
};
