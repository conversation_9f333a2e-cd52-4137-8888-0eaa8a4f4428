// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  STUDENT
  ALUMNI
  ADMIN
}

enum UserStatus {
  PENDING
  APPROVED
  REJECTED
  SUSPENDED
}

enum JobType {
  FULL_TIME
  PART_TIME
  INTERNSHIP
  CONTRACT
  FREELANCE
}

enum PostType {
  ADVICE
  GENERAL
  ANNOUNCEMENT
}

enum NotificationType {
  JOB_POSTED
  EVENT_CREATED
  MESSAGE_RECEIVED
  CONNECTION_REQUEST
  POST_CREATED
  SYSTEM
}

model User {
  id       String     @id @default(cuid())
  email    String     @unique
  password String
  name     String
  mobile   String?
  usn      String     @unique
  course   String
  batch    String
  role     UserRole   @default(STUDENT)
  status   UserStatus @default(PENDING)

  // Profile information
  profilePicture String?
  bio            String?
  linkedinUrl    String?
  githubUrl      String?
  portfolioUrl   String?

  // Alumni specific fields
  company    String?
  jobTitle   String?
  experience Int? // years of experience
  location   String?

  // Privacy settings
  showEmail    Boolean @default(false)
  showMobile   Boolean @default(false)
  showLinkedin Boolean @default(true)

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  lastLoginAt DateTime?

  // Relations
  jobsPosted              Job[]
  eventsCreated           Event[]
  posts                   Post[]
  sentMessages            Message[]                @relation("MessageSender")
  receivedMessages        Message[]                @relation("MessageReceiver")
  notifications           Notification[]
  jobApplications         JobApplication[]
  eventRSVPs              EventRSVP[]
  connections             Connection[]             @relation("ConnectionRequester")
  receivedConnections     Connection[]             @relation("ConnectionReceiver")
  notificationPreferences NotificationPreferences?

  @@index([email])
  @@index([status])
  @@index([role])
  @@index([course])
  @@index([batch])
  @@index([createdAt])
  @@index([status, role])
  @@index([course, batch])
  @@map("users")
}

model Job {
  id              String   @id @default(cuid())
  title           String
  company         String
  location        String
  type            JobType
  description     String
  requirements    String?
  salary          String?
  applicationUrl  String?
  allowResume     Boolean  @default(false)
  relevantCourses String[] // Array of course names
  isActive        Boolean  @default(true)

  // Timestamps
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  expiresAt DateTime?

  // Relations
  postedBy     User             @relation(fields: [postedById], references: [id], onDelete: Cascade)
  postedById   String
  applications JobApplication[]

  @@index([isActive])
  @@index([type])
  @@index([location])
  @@index([postedById])
  @@index([createdAt])
  @@index([isActive, type])
  @@index([isActive, createdAt])
  @@map("jobs")
}

model JobApplication {
  id        String  @id @default(cuid())
  resumeUrl String?
  message   String?
  status    String  @default("PENDING") // PENDING, REVIEWED, ACCEPTED, REJECTED

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  job         Job    @relation(fields: [jobId], references: [id], onDelete: Cascade)
  jobId       String
  applicant   User   @relation(fields: [applicantId], references: [id], onDelete: Cascade)
  applicantId String

  @@unique([jobId, applicantId])
  @@index([jobId])
  @@index([applicantId])
  @@index([status])
  @@index([createdAt])
  @@index([jobId, status])
  @@map("job_applications")
}

model Event {
  id           String    @id @default(cuid())
  title        String
  description  String
  location     String?
  imageUrl     String?
  startTime    DateTime
  endTime      DateTime?
  isOnline     Boolean   @default(false)
  meetingUrl   String?
  maxAttendees Int?
  isActive     Boolean   @default(true)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  organizer   User        @relation(fields: [organizerId], references: [id], onDelete: Cascade)
  organizerId String
  rsvps       EventRSVP[]

  @@index([isActive])
  @@index([organizerId])
  @@index([startTime])
  @@index([createdAt])
  @@index([isActive, startTime])
  @@index([startTime, endTime])
  @@map("events")
}

model EventRSVP {
  id     String @id @default(cuid())
  status String @default("GOING") // GOING, MAYBE, NOT_GOING

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  event   Event  @relation(fields: [eventId], references: [id], onDelete: Cascade)
  eventId String
  user    User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId  String

  @@unique([eventId, userId])
  @@index([eventId])
  @@index([userId])
  @@index([status])
  @@index([createdAt])
  @@map("event_rsvps")
}

model Post {
  id       String   @id @default(cuid())
  title    String
  content  String
  type     PostType @default(GENERAL)
  isPublic Boolean  @default(true)
  imageUrl String?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)
  authorId String

  @@index([authorId])
  @@index([type])
  @@index([isPublic])
  @@index([createdAt])
  @@index([isPublic, type])
  @@index([isPublic, createdAt])
  @@index([authorId, createdAt])
  @@map("posts")
}

model Message {
  id      String  @id @default(cuid())
  content String
  isRead  Boolean @default(false)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  sender     User   @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)
  senderId   String
  receiver   User   @relation("MessageReceiver", fields: [receiverId], references: [id], onDelete: Cascade)
  receiverId String

  @@index([senderId])
  @@index([receiverId])
  @@index([isRead])
  @@index([createdAt])
  @@index([senderId, receiverId])
  @@index([receiverId, isRead])
  @@index([senderId, createdAt])
  @@index([receiverId, createdAt])
  @@map("messages")
}

model Connection {
  id      String  @id @default(cuid())
  status  String  @default("PENDING") // PENDING, ACCEPTED, REJECTED, BLOCKED
  message String? // Initial connection message

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  requester   User   @relation("ConnectionRequester", fields: [requesterId], references: [id], onDelete: Cascade)
  requesterId String
  receiver    User   @relation("ConnectionReceiver", fields: [receiverId], references: [id], onDelete: Cascade)
  receiverId  String

  @@unique([requesterId, receiverId])
  @@index([requesterId])
  @@index([receiverId])
  @@index([status])
  @@index([createdAt])
  @@index([requesterId, status])
  @@index([receiverId, status])
  @@map("connections")
}

model Notification {
  id      String           @id @default(cuid())
  title   String
  message String
  type    NotificationType
  isRead  Boolean          @default(false)
  data    Json? // Additional data for the notification

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  @@index([userId])
  @@index([type])
  @@index([isRead])
  @@index([createdAt])
  @@index([userId, isRead])
  @@index([userId, type])
  @@index([userId, createdAt])
  @@map("notifications")
}

model NotificationPreferences {
  id String @id @default(cuid())

  // Email notification preferences
  emailJobPosted         Boolean @default(true)
  emailEventCreated      Boolean @default(true)
  emailMessageReceived   Boolean @default(true)
  emailConnectionRequest Boolean @default(true)
  emailPostCreated       Boolean @default(false)
  emailSystemUpdates     Boolean @default(true)

  // In-app notification preferences
  inAppJobPosted         Boolean @default(true)
  inAppEventCreated      Boolean @default(true)
  inAppMessageReceived   Boolean @default(true)
  inAppConnectionRequest Boolean @default(true)
  inAppPostCreated       Boolean @default(true)
  inAppSystemUpdates     Boolean @default(true)

  // General preferences
  emailDigest          Boolean @default(false) // Weekly digest email
  emailDigestFrequency String  @default("WEEKLY") // DAILY, WEEKLY, MONTHLY

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String @unique

  @@map("notification_preferences")
}
