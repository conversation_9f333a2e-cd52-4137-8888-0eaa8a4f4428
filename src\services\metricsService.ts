import { Logger } from "@/services/loggerService";
import { AdvancedCacheService } from "@/services/cacheService";
import { AdvancedQueueService } from "@/services/queueService";
import { getDatabaseMetrics } from "@/config/database";
import { ClusterManager } from "@/cluster";

// Performance metrics interface
interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  tags?: Record<string, string>;
}

// Request metrics interface
interface RequestMetric {
  method: string;
  path: string;
  statusCode: number;
  duration: number;
  timestamp: number;
  userAgent?: string;
  ip?: string;
  userId?: string;
}

// System metrics interface
interface SystemMetrics {
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
    heap: {
      used: number;
      total: number;
      percentage: number;
    };
  };
  uptime: number;
  timestamp: number;
}

export class MetricsService {
  private static metrics: PerformanceMetric[] = [];
  private static requestMetrics: RequestMetric[] = [];
  private static readonly MAX_METRICS = 10000; // Keep last 10k metrics
  private static readonly MAX_REQUEST_METRICS = 5000; // Keep last 5k request metrics

  // Initialize metrics collection
  static init() {
    // Start periodic system metrics collection
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000); // Every 30 seconds

    // Start periodic cleanup
    setInterval(() => {
      this.cleanupOldMetrics();
    }, 300000); // Every 5 minutes

    Logger.info("Metrics service initialized");
  }

  // Record a custom performance metric
  static recordMetric(name: string, value: number, unit: string = 'count', tags?: Record<string, string>) {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: Date.now(),
      tags,
    };

    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }

    Logger.debug(`Metric recorded: ${name} = ${value} ${unit}`, { tags });
  }

  // Record request metrics
  static recordRequest(requestData: Omit<RequestMetric, 'timestamp'>) {
    const requestMetric: RequestMetric = {
      ...requestData,
      timestamp: Date.now(),
    };

    this.requestMetrics.push(requestMetric);

    // Keep only recent request metrics
    if (this.requestMetrics.length > this.MAX_REQUEST_METRICS) {
      this.requestMetrics = this.requestMetrics.slice(-this.MAX_REQUEST_METRICS);
    }

    // Record as performance metric too
    this.recordMetric('http_request_duration', requestData.duration, 'ms', {
      method: requestData.method,
      path: requestData.path,
      status: requestData.statusCode.toString(),
    });

    this.recordMetric('http_request_total', 1, 'count', {
      method: requestData.method,
      path: requestData.path,
      status: requestData.statusCode.toString(),
    });
  }

  // Collect system metrics
  private static collectSystemMetrics() {
    try {
      const memUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      // Memory metrics
      this.recordMetric('system_memory_rss', memUsage.rss, 'bytes');
      this.recordMetric('system_memory_heap_total', memUsage.heapTotal, 'bytes');
      this.recordMetric('system_memory_heap_used', memUsage.heapUsed, 'bytes');
      this.recordMetric('system_memory_external', memUsage.external, 'bytes');

      // CPU metrics (convert microseconds to milliseconds)
      this.recordMetric('system_cpu_user', cpuUsage.user / 1000, 'ms');
      this.recordMetric('system_cpu_system', cpuUsage.system / 1000, 'ms');

      // Uptime
      this.recordMetric('system_uptime', process.uptime(), 'seconds');

      // Event loop lag (approximate)
      const start = process.hrtime.bigint();
      setImmediate(() => {
        const lag = Number(process.hrtime.bigint() - start) / 1000000; // Convert to ms
        this.recordMetric('system_event_loop_lag', lag, 'ms');
      });

    } catch (error) {
      Logger.error("Failed to collect system metrics", error);
    }
  }

  // Get comprehensive performance report
  static async getPerformanceReport() {
    try {
      const now = Date.now();
      const oneHourAgo = now - (60 * 60 * 1000);

      // Get recent metrics
      const recentMetrics = this.metrics.filter(m => m.timestamp > oneHourAgo);
      const recentRequests = this.requestMetrics.filter(r => r.timestamp > oneHourAgo);

      // Calculate request statistics
      const requestStats = this.calculateRequestStats(recentRequests);
      
      // Get system metrics
      const systemMetrics = this.getSystemMetrics();

      // Get external service metrics
      const [cacheStats, queueStats, dbMetrics] = await Promise.allSettled([
        AdvancedCacheService.getStats(),
        AdvancedQueueService.getQueueStats(),
        getDatabaseMetrics(),
      ]);

      // Get cluster info
      const clusterInfo = ClusterManager.getClusterInfo();

      return {
        timestamp: new Date().toISOString(),
        period: {
          start: new Date(oneHourAgo).toISOString(),
          end: new Date(now).toISOString(),
        },
        system: systemMetrics,
        requests: requestStats,
        cache: this.getResultValue(cacheStats),
        queues: this.getResultValue(queueStats),
        database: this.getResultValue(dbMetrics),
        cluster: clusterInfo,
        customMetrics: this.aggregateCustomMetrics(recentMetrics),
      };
    } catch (error) {
      Logger.error("Failed to generate performance report", error);
      throw error;
    }
  }

  // Calculate request statistics
  private static calculateRequestStats(requests: RequestMetric[]) {
    if (requests.length === 0) {
      return {
        total: 0,
        averageResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        errorRate: 0,
        requestsPerMinute: 0,
        statusCodes: {},
        topEndpoints: [],
      };
    }

    const durations = requests.map(r => r.duration).sort((a, b) => a - b);
    const errors = requests.filter(r => r.statusCode >= 400);
    
    // Calculate percentiles
    const p95Index = Math.floor(durations.length * 0.95);
    const p99Index = Math.floor(durations.length * 0.99);

    // Count status codes
    const statusCodes = requests.reduce((acc, req) => {
      acc[req.statusCode] = (acc[req.statusCode] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    // Top endpoints by request count
    const endpointCounts = requests.reduce((acc, req) => {
      const key = `${req.method} ${req.path}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topEndpoints = Object.entries(endpointCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([endpoint, count]) => ({ endpoint, count }));

    // Calculate time period for RPM
    const timeSpan = Math.max(requests[requests.length - 1].timestamp - requests[0].timestamp, 60000);
    const requestsPerMinute = (requests.length / timeSpan) * 60000;

    return {
      total: requests.length,
      averageResponseTime: durations.reduce((a, b) => a + b, 0) / durations.length,
      p95ResponseTime: durations[p95Index] || 0,
      p99ResponseTime: durations[p99Index] || 0,
      errorRate: (errors.length / requests.length) * 100,
      requestsPerMinute: Math.round(requestsPerMinute),
      statusCodes,
      topEndpoints,
    };
  }

  // Get current system metrics
  private static getSystemMetrics(): SystemMetrics {
    const memUsage = process.memoryUsage();
    
    return {
      cpu: {
        usage: 0, // Would need external library for accurate CPU usage
        loadAverage: [], // Not available on all platforms
      },
      memory: {
        used: memUsage.rss,
        total: memUsage.rss + memUsage.heapTotal, // Approximation
        percentage: (memUsage.heapUsed / memUsage.heapTotal) * 100,
        heap: {
          used: memUsage.heapUsed,
          total: memUsage.heapTotal,
          percentage: (memUsage.heapUsed / memUsage.heapTotal) * 100,
        },
      },
      uptime: process.uptime(),
      timestamp: Date.now(),
    };
  }

  // Aggregate custom metrics by name
  private static aggregateCustomMetrics(metrics: PerformanceMetric[]) {
    const aggregated = metrics.reduce((acc, metric) => {
      if (!acc[metric.name]) {
        acc[metric.name] = {
          name: metric.name,
          unit: metric.unit,
          count: 0,
          sum: 0,
          min: Infinity,
          max: -Infinity,
          avg: 0,
          latest: 0,
        };
      }

      const agg = acc[metric.name];
      agg.count++;
      agg.sum += metric.value;
      agg.min = Math.min(agg.min, metric.value);
      agg.max = Math.max(agg.max, metric.value);
      agg.avg = agg.sum / agg.count;
      agg.latest = metric.value;

      return acc;
    }, {} as Record<string, any>);

    return Object.values(aggregated);
  }

  // Clean up old metrics
  private static cleanupOldMetrics() {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
    
    const beforeMetrics = this.metrics.length;
    const beforeRequests = this.requestMetrics.length;
    
    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
    this.requestMetrics = this.requestMetrics.filter(r => r.timestamp > cutoff);
    
    const cleanedMetrics = beforeMetrics - this.metrics.length;
    const cleanedRequests = beforeRequests - this.requestMetrics.length;
    
    if (cleanedMetrics > 0 || cleanedRequests > 0) {
      Logger.debug(`Cleaned up ${cleanedMetrics} metrics and ${cleanedRequests} request metrics`);
    }
  }

  // Get metrics for specific time range
  static getMetricsInRange(startTime: number, endTime: number) {
    return {
      metrics: this.metrics.filter(m => m.timestamp >= startTime && m.timestamp <= endTime),
      requests: this.requestMetrics.filter(r => r.timestamp >= startTime && r.timestamp <= endTime),
    };
  }

  // Get real-time metrics (last 5 minutes)
  static getRealTimeMetrics() {
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    return this.getMetricsInRange(fiveMinutesAgo, Date.now());
  }

  // Helper to extract result from Promise.allSettled
  private static getResultValue<T>(result: PromiseSettledResult<T>): T | { error: string } {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      return { error: result.reason?.message || String(result.reason) };
    }
  }

  // Export metrics in Prometheus format
  static exportPrometheusMetrics(): string {
    const lines: string[] = [];
    
    // Add custom metrics
    const aggregated = this.aggregateCustomMetrics(this.metrics);
    
    for (const metric of aggregated) {
      lines.push(`# HELP ${metric.name} ${metric.name}`);
      lines.push(`# TYPE ${metric.name} gauge`);
      lines.push(`${metric.name} ${metric.latest}`);
    }

    return lines.join('\n');
  }

  // Get current metrics summary
  static getMetricsSummary() {
    return {
      totalMetrics: this.metrics.length,
      totalRequests: this.requestMetrics.length,
      oldestMetric: this.metrics.length > 0 ? new Date(this.metrics[0].timestamp).toISOString() : null,
      newestMetric: this.metrics.length > 0 ? new Date(this.metrics[this.metrics.length - 1].timestamp).toISOString() : null,
      memoryUsage: {
        metrics: `${(JSON.stringify(this.metrics).length / 1024 / 1024).toFixed(2)} MB`,
        requests: `${(JSON.stringify(this.requestMetrics).length / 1024 / 1024).toFixed(2)} MB`,
      },
    };
  }
}

// Initialize metrics service
MetricsService.init();
