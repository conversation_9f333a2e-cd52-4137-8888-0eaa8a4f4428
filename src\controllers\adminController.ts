import { Request, Response, NextFunction } from "express";
import { UserRole, UserStatus } from "@prisma/client";
import { prisma } from "@/config/database";
import { createError } from "@/middleware/errorHandler";
import { sendApprovalEmail } from "@/services/emailService";
import { createNotification } from "@/services/notificationService";

/**
 * Get admin dashboard overview
 */
export const getDashboard = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get key metrics for dashboard
    const [
      totalUsers,
      pendingUsers,
      totalJobs,
      activeJobs,
      totalEvents,
      upcomingEvents,
      totalPosts,
      totalMessages,
      recentActivity,
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { status: UserStatus.PENDING } }),
      prisma.job.count(),
      prisma.job.count({ where: { isActive: true } }),
      prisma.event.count(),
      prisma.event.count({
        where: {
          isActive: true,
          startTime: { gte: new Date() },
        },
      }),
      prisma.post.count(),
      prisma.message.count(),
      // Recent registrations in last 7 days
      prisma.user.findMany({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          status: true,
          createdAt: true,
        },
        orderBy: { createdAt: "desc" },
        take: 10,
      }),
    ]);

    // User distribution by role
    const usersByRole = await prisma.user.groupBy({
      by: ["role"],
      _count: { role: true },
      where: { status: UserStatus.APPROVED },
    });

    // User distribution by status
    const usersByStatus = await prisma.user.groupBy({
      by: ["status"],
      _count: { status: true },
    });

    res.json({
      metrics: {
        users: {
          total: totalUsers,
          pending: pendingUsers,
          approved: totalUsers - pendingUsers,
          byRole: usersByRole,
          byStatus: usersByStatus,
        },
        jobs: {
          total: totalJobs,
          active: activeJobs,
          inactive: totalJobs - activeJobs,
        },
        events: {
          total: totalEvents,
          upcoming: upcomingEvents,
        },
        content: {
          posts: totalPosts,
          messages: totalMessages,
        },
      },
      recentActivity,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get detailed metrics
 */
export const getMetrics = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const days = parseInt(req.query.days as string) || 30;
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    // Registration trends
    const registrationTrends = await prisma.$queryRaw<
      Array<{
        date: string;
        count: number;
      }>
    >`
      SELECT 
        DATE("createdAt") as date,
        COUNT(*)::int as count
      FROM users 
      WHERE "createdAt" >= ${startDate}
      GROUP BY DATE("createdAt")
      ORDER BY date ASC
    `;

    // Job posting trends
    const jobTrends = await prisma.$queryRaw<
      Array<{
        date: string;
        count: number;
      }>
    >`
      SELECT 
        DATE("createdAt") as date,
        COUNT(*)::int as count
      FROM jobs 
      WHERE "createdAt" >= ${startDate}
      GROUP BY DATE("createdAt")
      ORDER BY date ASC
    `;

    // Event trends
    const eventTrends = await prisma.$queryRaw<
      Array<{
        date: string;
        count: number;
      }>
    >`
      SELECT 
        DATE("createdAt") as date,
        COUNT(*)::int as count
      FROM events 
      WHERE "createdAt" >= ${startDate}
      GROUP BY DATE("createdAt")
      ORDER BY date ASC
    `;

    // Most active users
    const activeUsers = await prisma.$queryRaw<
      Array<{
        userId: string;
        name: string;
        email: string;
        role: string;
        activityCount: number;
      }>
    >`
      SELECT 
        u.id as "userId",
        u.name,
        u.email,
        u.role,
        (
          COALESCE((SELECT COUNT(*) FROM jobs WHERE "postedById" = u.id), 0) +
          COALESCE((SELECT COUNT(*) FROM events WHERE "organizerId" = u.id), 0) +
          COALESCE((SELECT COUNT(*) FROM posts WHERE "authorId" = u.id), 0) +
          COALESCE((SELECT COUNT(*) FROM messages WHERE "senderId" = u.id), 0)
        )::int as "activityCount"
      FROM users u
      WHERE u.status = 'APPROVED'
      ORDER BY "activityCount" DESC
      LIMIT 10
    `;

    res.json({
      trends: {
        registrations: registrationTrends,
        jobs: jobTrends,
        events: eventTrends,
      },
      activeUsers,
      period: `${days} days`,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all users with filtering
 */
export const getAllUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const status = req.query.status as UserStatus;
    const role = req.query.role as UserRole;
    const search = req.query.search as string;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (role) {
      where.role = role;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { usn: { contains: search, mode: "insensitive" } },
      ];
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          email: true,
          name: true,
          usn: true,
          course: true,
          batch: true,
          role: true,
          status: true,
          company: true,
          jobTitle: true,
          createdAt: true,
          lastLoginAt: true,
          _count: {
            select: {
              jobsPosted: true,
              eventsCreated: true,
              posts: true,
              sentMessages: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.user.count({ where }),
    ]);

    res.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get pending users for approval
 */
export const getPendingUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where: { status: UserStatus.PENDING },
        select: {
          id: true,
          email: true,
          name: true,
          mobile: true,
          usn: true,
          course: true,
          batch: true,
          role: true,
          bio: true,
          createdAt: true,
        },
        skip,
        take: limit,
        orderBy: { createdAt: "asc" }, // Oldest first for approval queue
      }),
      prisma.user.count({ where: { status: UserStatus.PENDING } }),
    ]);

    res.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Approve user
 */
export const approveUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        status: true,
      },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    if (user.status !== UserStatus.PENDING) {
      throw createError("User is not pending approval", 400);
    }

    // Update user status
    const updatedUser = await prisma.user.update({
      where: { id },
      data: { status: UserStatus.APPROVED },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
      },
    });

    // Send approval email
    try {
      await sendApprovalEmail(user.email, user.name);
    } catch (emailError) {
      console.error("Failed to send approval email:", emailError);
      // Don't fail the approval if email fails
    }

    // Create notification
    await createNotification({
      userId: id,
      type: "SYSTEM",
      title: "Account Approved",
      message: "Your account has been approved! You can now access all features of the Alumni Portal.",
      data: { approvedBy: req.user?.userId },
    });

    res.json({
      message: "User approved successfully",
      user: updatedUser,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

// Export additional methods from adminController2
export { rejectUser, suspendUser, activateUser, deleteUser, getAllJobs, getAllEvents } from "./adminController2";

// Placeholder methods for remaining functionality
export const getReportedPosts = async (req: Request, res: Response, next: NextFunction) => {
  res.status(501).json({ message: "Reported posts feature coming soon" });
};

export const hidePost = async (req: Request, res: Response, next: NextFunction) => {
  res.status(501).json({ message: "Hide post feature coming soon" });
};

export const showPost = async (req: Request, res: Response, next: NextFunction) => {
  res.status(501).json({ message: "Show post feature coming soon" });
};

export const deletePost = async (req: Request, res: Response, next: NextFunction) => {
  res.status(501).json({ message: "Delete post feature coming soon" });
};

export const getUserReports = async (req: Request, res: Response, next: NextFunction) => {
  res.status(501).json({ message: "User reports feature coming soon" });
};

export const getContentReports = async (req: Request, res: Response, next: NextFunction) => {
  res.status(501).json({ message: "Content reports feature coming soon" });
};

export const getActivityReports = async (req: Request, res: Response, next: NextFunction) => {
  res.status(501).json({ message: "Activity reports feature coming soon" });
};
