import winston from 'winston';
import path from 'path';

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each log level
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Add colors to winston
winston.addColors(logColors);

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Create transports
const transports = [];

// Console transport for development
if (process.env.NODE_ENV === 'development') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
      level: 'debug',
    })
  );
} else {
  // Production console transport (less verbose)
  transports.push(
    new winston.transports.Console({
      format: logFormat,
      level: 'info',
    })
  );
}

// File transports for production
if (process.env.NODE_ENV === 'production') {
  // Error log file
  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );

  // Combined log file
  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );

  // HTTP access log
  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, 'access.log'),
      level: 'http',
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );
}

// Create the logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'development' ? 'debug' : 'info'),
  levels: logLevels,
  format: logFormat,
  transports,
  exitOnError: false,
});

// Enhanced logger with additional methods
export class Logger {
  // Standard logging methods
  static error(message: string, meta?: any) {
    logger.error(message, meta);
  }

  static warn(message: string, meta?: any) {
    logger.warn(message, meta);
  }

  static info(message: string, meta?: any) {
    logger.info(message, meta);
  }

  static http(message: string, meta?: any) {
    logger.http(message, meta);
  }

  static debug(message: string, meta?: any) {
    logger.debug(message, meta);
  }

  // Application-specific logging methods
  static auth(message: string, userId?: string, meta?: any) {
    logger.info(`[AUTH] ${message}`, { userId, ...meta });
  }

  static database(message: string, query?: string, meta?: any) {
    logger.debug(`[DATABASE] ${message}`, { query, ...meta });
  }

  static api(message: string, endpoint?: string, method?: string, meta?: any) {
    logger.http(`[API] ${message}`, { endpoint, method, ...meta });
  }

  static security(message: string, ip?: string, meta?: any) {
    logger.warn(`[SECURITY] ${message}`, { ip, ...meta });
  }

  static performance(message: string, duration?: number, meta?: any) {
    logger.info(`[PERFORMANCE] ${message}`, { duration, ...meta });
  }

  static queue(message: string, queueName?: string, jobId?: string, meta?: any) {
    logger.info(`[QUEUE] ${message}`, { queueName, jobId, ...meta });
  }

  static cache(message: string, key?: string, meta?: any) {
    logger.debug(`[CACHE] ${message}`, { key, ...meta });
  }

  static websocket(message: string, userId?: string, meta?: any) {
    logger.debug(`[WEBSOCKET] ${message}`, { userId, ...meta });
  }

  // Request logging middleware
  static requestLogger() {
    return (req: any, res: any, next: any) => {
      const start = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - start;
        const { method, url, ip } = req;
        const { statusCode } = res;
        
        const message = `${method} ${url} ${statusCode} - ${duration}ms`;
        
        if (statusCode >= 400) {
          Logger.error(message, {
            method,
            url,
            statusCode,
            duration,
            ip,
            userAgent: req.get('User-Agent'),
            userId: req.user?.userId,
          });
        } else {
          Logger.http(message, {
            method,
            url,
            statusCode,
            duration,
            ip,
            userId: req.user?.userId,
          });
        }
      });
      
      next();
    };
  }

  // Error logging middleware
  static errorLogger() {
    return (err: any, req: any, res: any, next: any) => {
      const { method, url, ip } = req;
      
      Logger.error(`Unhandled error: ${err.message}`, {
        error: err.stack,
        method,
        url,
        ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.userId,
        body: req.body,
        params: req.params,
        query: req.query,
      });
      
      next(err);
    };
  }

  // Performance monitoring
  static async measurePerformance<T>(
    operation: string,
    fn: () => Promise<T>,
    meta?: any
  ): Promise<T> {
    const start = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - start;
      
      Logger.performance(`${operation} completed`, duration, meta);
      
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      
      Logger.error(`${operation} failed after ${duration}ms`, {
        error: error.message,
        duration,
        ...meta,
      });
      
      throw error;
    }
  }

  // Log application startup
  static startup(message: string, meta?: any) {
    logger.info(`[STARTUP] ${message}`, meta);
  }

  // Log application shutdown
  static shutdown(message: string, meta?: any) {
    logger.info(`[SHUTDOWN] ${message}`, meta);
  }

  // Health check logging
  static health(service: string, status: 'healthy' | 'unhealthy', meta?: any) {
    const level = status === 'healthy' ? 'info' : 'error';
    logger[level](`[HEALTH] ${service} is ${status}`, meta);
  }

  // Business logic logging
  static business(event: string, userId?: string, meta?: any) {
    logger.info(`[BUSINESS] ${event}`, { userId, ...meta });
  }
}

// Create a stream for Morgan HTTP logging
export const morganStream = {
  write: (message: string) => {
    Logger.http(message.trim());
  },
};

// Export the winston logger instance for advanced usage
export { logger };

// Default export
export default Logger;
