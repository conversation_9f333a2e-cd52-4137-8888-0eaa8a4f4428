apiVersion: v1
kind: ConfigMap
metadata:
  name: alumni-portal-config
  namespace: alumni-portal
data:
  NODE_ENV: "production"
  PORT: "3000"
  WS_PORT: "3001"
  CLUSTER_ENABLED: "false"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  MEMORY_CACHE_MAX_SIZE: "1000"
  MEMORY_CACHE_TTL: "300"
  MEMORY_CACHE_CLEANUP_INTERVAL: "60000"
  RATE_LIMIT_WINDOW_MS: "900000"
  RATE_LIMIT_MAX_REQUESTS: "100"
  LOG_LEVEL: "info"
