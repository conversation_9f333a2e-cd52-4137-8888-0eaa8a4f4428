import { Router } from "express";
import { authenticate, requireApproved } from "@/middleware/auth";
import { uploadMiddleware } from "@/middleware/upload";
import * as uploadController from "@/controllers/uploadController";

const router = Router();

// All routes require authentication
router.use(authenticate);
router.use(requireApproved);

// File upload routes
router.post("/profile-picture", uploadMiddleware.single("profilePicture"), uploadController.uploadProfilePicture);

router.post("/resume", uploadMiddleware.single("resume"), uploadController.uploadResume);

router.post("/event-image", uploadMiddleware.single("eventImage"), uploadController.uploadEventImage);

router.post("/post-image", uploadMiddleware.single("postImage"), uploadController.uploadPostImage);

// File deletion routes
router.delete("/file/:publicId", uploadController.deleteFile);

export default router;
