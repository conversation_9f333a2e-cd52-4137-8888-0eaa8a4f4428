apiVersion: apps/v1
kind: Deployment
metadata:
  name: alumni-portal-api
  namespace: alumni-portal
  labels:
    app: alumni-portal-api
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: alumni-portal-api
  template:
    metadata:
      labels:
        app: alumni-portal-api
    spec:
      containers:
      - name: api
        image: alumni-portal-api:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        - containerPort: 3001
          name: websocket
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: alumni-portal-secrets
              key: DATABASE_URL
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: alumni-portal-secrets
              key: JWT_SECRET
        - name: JWT_REFRESH_SECRET
          valueFrom:
            secretKeyRef:
              name: alumni-portal-secrets
              key: JWT_REFRESH_SECRET
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: alumni-portal-secrets
              key: REDIS_PASSWORD
        - name: CLOUDINARY_API_SECRET
          valueFrom:
            secretKeyRef:
              name: alumni-portal-secrets
              key: CLOUDINARY_API_SECRET
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: alumni-portal-secrets
              key: SMTP_PASS
        envFrom:
        - configMapRef:
            name: alumni-portal-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: uploads
          mountPath: /app/uploads
      volumes:
      - name: logs
        emptyDir: {}
      - name: uploads
        persistentVolumeClaim:
          claimName: alumni-portal-uploads-pvc
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: alumni-portal-api-service
  namespace: alumni-portal
  labels:
    app: alumni-portal-api
spec:
  selector:
    app: alumni-portal-api
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
  - name: websocket
    port: 3001
    targetPort: 3001
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: alumni-portal-uploads-pvc
  namespace: alumni-portal
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
