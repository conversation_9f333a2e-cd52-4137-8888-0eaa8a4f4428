# Alumni Portal API Documentation

## Overview

The Alumni Portal API is a comprehensive RESTful API built with Node.js, Express, and TypeScript. It provides endpoints for user management, job postings, events, messaging, notifications, and more.

## Base URL

```
http://localhost:3000/api
```

## Authentication

The API uses JW<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow a consistent format:

```json
{
  "message": "Success message",
  "data": {}, // Response data
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

Error responses:

```json
{
  "error": "Error message",
  "details": [], // Validation errors (if any)
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Endpoints

### Authentication

#### POST /api/auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "<PERSON>",
  "mobile": "+**********",
  "usn": "1AB12CS001",
  "course": "Computer Science",
  "batch": "2020",
  "role": "STUDENT" // or "ALUMNI"
}
```

#### POST /api/auth/login
Login with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### POST /api/auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "your-refresh-token"
}
```

#### POST /api/auth/logout
Logout and invalidate tokens.

### User Profile

#### GET /api/profile
Get current user profile.

#### PUT /api/profile
Update user profile.

**Request Body:**
```json
{
  "name": "Updated Name",
  "bio": "Updated bio",
  "company": "Company Name",
  "jobTitle": "Job Title",
  "location": "City, Country"
}
```

#### PUT /api/profile/password
Change password.

**Request Body:**
```json
{
  "currentPassword": "current-password",
  "newPassword": "new-password"
}
```

### Jobs

#### GET /api/jobs
Get all jobs with filtering and pagination.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20)
- `type` (string): Job type filter
- `location` (string): Location filter
- `search` (string): Search in title and description

#### POST /api/jobs
Create a new job posting (Alumni only).

**Request Body:**
```json
{
  "title": "Software Engineer",
  "company": "Tech Company",
  "location": "San Francisco, CA",
  "type": "FULL_TIME",
  "description": "Job description...",
  "requirements": "Job requirements...",
  "salary": "$80,000 - $120,000",
  "applicationUrl": "https://company.com/apply",
  "relevantCourses": ["Computer Science", "Software Engineering"]
}
```

#### GET /api/jobs/:id
Get job details by ID.

#### PUT /api/jobs/:id
Update job posting (Owner only).

#### DELETE /api/jobs/:id
Delete job posting (Owner only).

#### POST /api/jobs/:id/apply
Apply for a job.

**Request Body:**
```json
{
  "resumeUrl": "https://cloudinary.com/resume.pdf",
  "message": "Cover letter message"
}
```

### Events

#### GET /api/events
Get all events with filtering and pagination.

#### POST /api/events
Create a new event (Alumni only).

**Request Body:**
```json
{
  "title": "Alumni Meetup",
  "description": "Event description...",
  "location": "Conference Room A",
  "startTime": "2024-01-15T18:00:00Z",
  "endTime": "2024-01-15T20:00:00Z",
  "maxAttendees": 50
}
```

#### GET /api/events/:id
Get event details by ID.

#### PUT /api/events/:id
Update event (Owner only).

#### DELETE /api/events/:id
Delete event (Owner only).

#### POST /api/events/:id/rsvp
RSVP to an event.

**Request Body:**
```json
{
  "status": "GOING" // or "MAYBE", "NOT_GOING"
}
```

### Posts

#### GET /api/posts
Get all public posts with filtering and pagination.

#### POST /api/posts
Create a new post (Alumni only).

**Request Body:**
```json
{
  "title": "Career Advice",
  "content": "Post content...",
  "type": "ADVICE", // or "GENERAL", "ANNOUNCEMENT"
  "isPublic": true,
  "imageUrl": "https://cloudinary.com/image.jpg"
}
```

#### GET /api/posts/:id
Get post details by ID.

#### PUT /api/posts/:id
Update post (Owner only).

#### DELETE /api/posts/:id
Delete post (Owner only).

#### GET /api/posts/my
Get current user's posts.

#### GET /api/posts/feed
Get personalized dashboard feed.

### Messages

#### GET /api/messages/conversations
Get user's conversations.

#### GET /api/messages/conversations/:userId
Get conversation with specific user.

#### POST /api/messages/send
Send a message.

**Request Body:**
```json
{
  "receiverId": "user-id",
  "content": "Message content"
}
```

#### PUT /api/messages/:id/read
Mark message as read.

#### GET /api/messages/unread/count
Get unread message count.

### Notifications

#### GET /api/notifications
Get user's notifications.

#### GET /api/notifications/unread/count
Get unread notification count.

#### PUT /api/notifications/:id/read
Mark notification as read.

#### PUT /api/notifications/mark-all-read
Mark all notifications as read.

#### DELETE /api/notifications/:id
Delete notification.

#### GET /api/notifications/preferences
Get notification preferences.

#### PUT /api/notifications/preferences
Update notification preferences.

**Request Body:**
```json
{
  "emailJobPosted": true,
  "emailEventCreated": true,
  "emailMessageReceived": false,
  "inAppJobPosted": true,
  "emailDigest": false
}
```

### Connections

#### GET /api/connections
Get user's connections.

#### POST /api/connections/request
Send connection request.

**Request Body:**
```json
{
  "receiverId": "user-id",
  "message": "Optional message"
}
```

#### PUT /api/connections/:id/respond
Respond to connection request.

**Request Body:**
```json
{
  "status": "ACCEPTED" // or "REJECTED", "BLOCKED"
}
```

#### GET /api/connections/requests/received
Get received connection requests.

#### GET /api/connections/requests/sent
Get sent connection requests.

### File Uploads

#### POST /api/uploads/profile-picture
Upload profile picture.

**Form Data:**
- `profilePicture` (file): Image file (JPEG, PNG, GIF, WebP, max 5MB)

#### POST /api/uploads/resume
Upload resume.

**Form Data:**
- `resume` (file): PDF or Word document (max 10MB)

#### POST /api/uploads/event-image
Upload event image.

**Form Data:**
- `eventImage` (file): Image file
- `eventId` (string): Event ID

#### POST /api/uploads/post-image
Upload post image.

**Form Data:**
- `postImage` (file): Image file
- `postId` (string): Post ID

#### DELETE /api/uploads/file/:publicId
Delete file from Cloudinary.

### Admin (Admin only)

#### GET /api/admin/dashboard
Get admin dashboard metrics.

#### GET /api/admin/users/pending
Get pending user approvals.

#### PUT /api/admin/users/:id/approve
Approve user account.

#### PUT /api/admin/users/:id/reject
Reject user account.

#### PUT /api/admin/users/:id/suspend
Suspend user account.

#### GET /api/admin/metrics
Get detailed analytics.

## Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (duplicate data)
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error

## Rate Limiting

API requests are rate-limited to 100 requests per 15-minute window per IP address.

## WebSocket Events

The API also supports real-time features via WebSocket connections on port 3001.

### Connection
```javascript
const ws = new WebSocket('ws://localhost:3001');
```

### Authentication
```javascript
ws.send(JSON.stringify({
  type: 'auth',
  token: 'your-jwt-token'
}));
```

### Message Types
- `new_message` - Real-time message received
- `new_notification` - Real-time notification
- `message_sent` - Message delivery confirmation
- `auth_success` - Authentication successful
- `auth_error` - Authentication failed
