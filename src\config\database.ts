import { PrismaClient, Prisma } from "@prisma/client";

declare global {
  var __prisma: PrismaClient | undefined;
}

// Database configuration for optimal performance
const getDatabaseConfig = (): Prisma.PrismaClientOptions => {
  const isProduction = process.env.NODE_ENV === "production";

  return {
    // Logging configuration
    log: isProduction
      ? [{ level: "error", emit: "stdout" }]
      : [
          { level: "query", emit: "stdout" },
          { level: "error", emit: "stdout" },
          { level: "warn", emit: "stdout" },
        ],

    // Connection timeout configuration
    // Note: Prisma handles connection pooling internally
    // These are the available configuration options
  };
};

// Prevent multiple instances of Prisma Client in development
const prisma = globalThis.__prisma || new PrismaClient(getDatabaseConfig());

if (process.env.NODE_ENV === "development") {
  globalThis.__prisma = prisma;
}

// Database health check function
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error("Database health check failed:", error);
    return false;
  }
};

// Database metrics collection
export const getDatabaseMetrics = async () => {
  try {
    const [userCount, jobCount, eventCount, messageCount] = await Promise.all([
      prisma.user.count(),
      prisma.job.count(),
      prisma.event.count(),
      prisma.message.count(),
    ]);

    return {
      users: userCount,
      jobs: jobCount,
      events: eventCount,
      messages: messageCount,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Failed to collect database metrics:", error);
    return null;
  }
};

// Connection pool monitoring
export const getConnectionInfo = () => {
  return {
    // Note: Prisma doesn't expose connection pool info directly
    // This would be enhanced with custom connection pool implementation
    status: "connected",
    timestamp: new Date().toISOString(),
  };
};

export { prisma };

// Enhanced graceful shutdown with connection cleanup
const gracefulShutdown = async (signal: string) => {
  console.log(`Received ${signal}, starting graceful shutdown...`);

  try {
    await prisma.$disconnect();
    console.log("Database connections closed successfully");
  } catch (error) {
    console.error("Error during database shutdown:", error);
  }

  process.exit(0);
};

// Handle various shutdown signals
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));
process.on("beforeExit", async () => {
  await prisma.$disconnect();
});
