import { checkDatabaseHealth, checkReadReplicaHealth, getDatabaseMetrics } from "@/config/database";
import { checkRedisHealth, CacheService } from "@/config/redis";
import { AdvancedCacheService } from "@/services/cacheService";
import { ClusterManager } from "@/cluster";
import { Logger } from "@/services/loggerService";

// Health check status enum
export enum HealthStatus {
  HEALTHY = "healthy",
  UNHEALTHY = "unhealthy",
  DEGRADED = "degraded",
}

// Health check result interface
interface HealthCheckResult {
  status: HealthStatus;
  message: string;
  responseTime?: number;
  details?: any;
}

// Overall health check result
interface OverallHealthResult {
  status: HealthStatus;
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: HealthCheckResult;
    readReplica: HealthCheckResult;
    redis: HealthCheckResult;
    cache: HealthCheckResult;
    cluster: HealthCheckResult;
  };
  metrics: {
    memory: any;
    database?: any;
    cache: any;
  };
}

export class HealthService {
  private static readonly version = process.env.npm_package_version || "1.0.0";

  // Comprehensive health check
  static async getHealthStatus(): Promise<OverallHealthResult> {
    const startTime = Date.now();
    
    try {
      // Run all health checks in parallel
      const [
        databaseHealth,
        readReplicaHealth,
        redisHealth,
        cacheHealth,
        clusterHealth,
        databaseMetrics,
      ] = await Promise.allSettled([
        this.checkDatabaseHealth(),
        this.checkReadReplicaHealth(),
        this.checkRedisHealth(),
        this.checkCacheHealth(),
        this.checkClusterHealth(),
        getDatabaseMetrics(),
      ]);

      // Get system metrics
      const memoryUsage = process.memoryUsage();
      const cacheStats = AdvancedCacheService.getStats();

      // Determine overall status
      const services = {
        database: this.getResultValue(databaseHealth),
        readReplica: this.getResultValue(readReplicaHealth),
        redis: this.getResultValue(redisHealth),
        cache: this.getResultValue(cacheHealth),
        cluster: this.getResultValue(clusterHealth),
      };

      const overallStatus = this.determineOverallStatus(services);
      const totalResponseTime = Date.now() - startTime;

      const result: OverallHealthResult = {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: this.version,
        environment: process.env.NODE_ENV || "development",
        services,
        metrics: {
          memory: {
            rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
            heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
            heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
            external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
          },
          database: this.getResultValue(databaseMetrics),
          cache: cacheStats,
        },
      };

      // Log health check results
      Logger.health("system", overallStatus, {
        responseTime: `${totalResponseTime}ms`,
        services: Object.entries(services).map(([name, result]) => ({
          name,
          status: result.status,
          responseTime: result.responseTime,
        })),
      });

      return result;
    } catch (error) {
      Logger.error("Health check failed", error);
      
      return {
        status: HealthStatus.UNHEALTHY,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: this.version,
        environment: process.env.NODE_ENV || "development",
        services: {
          database: { status: HealthStatus.UNHEALTHY, message: "Health check failed" },
          readReplica: { status: HealthStatus.UNHEALTHY, message: "Health check failed" },
          redis: { status: HealthStatus.UNHEALTHY, message: "Health check failed" },
          cache: { status: HealthStatus.UNHEALTHY, message: "Health check failed" },
          cluster: { status: HealthStatus.UNHEALTHY, message: "Health check failed" },
        },
        metrics: {
          memory: process.memoryUsage(),
          cache: { error: "Failed to get cache stats" },
        },
      };
    }
  }

  // Database health check
  private static async checkDatabaseHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const isHealthy = await checkDatabaseHealth();
      const responseTime = Date.now() - startTime;
      
      return {
        status: isHealthy ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
        message: isHealthy ? "Database connection successful" : "Database connection failed",
        responseTime,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        status: HealthStatus.UNHEALTHY,
        message: `Database health check failed: ${error.message}`,
        responseTime,
      };
    }
  }

  // Read replica health check
  private static async checkReadReplicaHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const isHealthy = await checkReadReplicaHealth();
      const responseTime = Date.now() - startTime;
      
      return {
        status: isHealthy ? HealthStatus.HEALTHY : HealthStatus.DEGRADED,
        message: isHealthy ? "Read replica connection successful" : "Read replica connection failed",
        responseTime,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        status: HealthStatus.DEGRADED,
        message: `Read replica health check failed: ${error.message}`,
        responseTime,
      };
    }
  }

  // Redis health check
  private static async checkRedisHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const isHealthy = await checkRedisHealth();
      const responseTime = Date.now() - startTime;
      
      return {
        status: isHealthy ? HealthStatus.HEALTHY : HealthStatus.DEGRADED,
        message: isHealthy ? "Redis connection successful" : "Redis connection failed",
        responseTime,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        status: HealthStatus.DEGRADED,
        message: `Redis health check failed: ${error.message}`,
        responseTime,
      };
    }
  }

  // Cache health check
  private static async checkCacheHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Test cache operations
      const testKey = "health-check-test";
      const testValue = { timestamp: Date.now() };
      
      await AdvancedCacheService.set(testKey, testValue, 10);
      const retrieved = await AdvancedCacheService.get(testKey);
      await AdvancedCacheService.delete(testKey);
      
      const responseTime = Date.now() - startTime;
      const isHealthy = retrieved !== null && retrieved.timestamp === testValue.timestamp;
      
      return {
        status: isHealthy ? HealthStatus.HEALTHY : HealthStatus.DEGRADED,
        message: isHealthy ? "Cache operations successful" : "Cache operations failed",
        responseTime,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        status: HealthStatus.DEGRADED,
        message: `Cache health check failed: ${error.message}`,
        responseTime,
      };
    }
  }

  // Cluster health check
  private static async checkClusterHealth(): Promise<HealthCheckResult> {
    try {
      const clusterInfo = ClusterManager.getClusterInfo();
      
      if (clusterInfo.isMaster) {
        const healthyWorkers = clusterInfo.aliveWorkers || 0;
        const totalWorkers = clusterInfo.totalWorkers || 0;
        
        if (healthyWorkers === totalWorkers && totalWorkers > 0) {
          return {
            status: HealthStatus.HEALTHY,
            message: `All ${totalWorkers} workers are healthy`,
            details: clusterInfo,
          };
        } else if (healthyWorkers > 0) {
          return {
            status: HealthStatus.DEGRADED,
            message: `${healthyWorkers}/${totalWorkers} workers are healthy`,
            details: clusterInfo,
          };
        } else {
          return {
            status: HealthStatus.UNHEALTHY,
            message: "No healthy workers available",
            details: clusterInfo,
          };
        }
      } else {
        return {
          status: HealthStatus.HEALTHY,
          message: `Worker process ${clusterInfo.workerPid} is healthy`,
          details: clusterInfo,
        };
      }
    } catch (error) {
      return {
        status: HealthStatus.HEALTHY,
        message: "Single process mode (clustering disabled)",
        details: { clustering: false },
      };
    }
  }

  // Determine overall system status
  private static determineOverallStatus(services: Record<string, HealthCheckResult>): HealthStatus {
    const statuses = Object.values(services).map(service => service.status);
    
    if (statuses.every(status => status === HealthStatus.HEALTHY)) {
      return HealthStatus.HEALTHY;
    }
    
    if (statuses.some(status => status === HealthStatus.UNHEALTHY)) {
      // Critical services that make the system unhealthy
      if (services.database.status === HealthStatus.UNHEALTHY) {
        return HealthStatus.UNHEALTHY;
      }
    }
    
    return HealthStatus.DEGRADED;
  }

  // Helper to extract result from Promise.allSettled
  private static getResultValue<T>(result: PromiseSettledResult<T>): T | HealthCheckResult {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      return {
        status: HealthStatus.UNHEALTHY,
        message: `Check failed: ${result.reason?.message || result.reason}`,
      };
    }
  }

  // Readiness check (lighter than full health check)
  static async getReadinessStatus(): Promise<{ ready: boolean; message: string }> {
    try {
      const [dbHealthy, redisHealthy] = await Promise.all([
        checkDatabaseHealth(),
        checkRedisHealth(),
      ]);

      const ready = dbHealthy; // Redis is not critical for readiness
      
      return {
        ready,
        message: ready 
          ? "Service is ready to accept traffic"
          : "Service is not ready - database unavailable",
      };
    } catch (error) {
      return {
        ready: false,
        message: `Readiness check failed: ${error.message}`,
      };
    }
  }

  // Liveness check (minimal check to ensure process is alive)
  static getLivenessStatus(): { alive: boolean; message: string } {
    try {
      // Simple check - if we can execute this code, the process is alive
      const memoryUsage = process.memoryUsage();
      
      // Check if memory usage is reasonable (not a memory leak)
      const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
      const maxHeapMB = 1024; // 1GB threshold
      
      if (heapUsedMB > maxHeapMB) {
        return {
          alive: false,
          message: `Memory usage too high: ${Math.round(heapUsedMB)}MB`,
        };
      }
      
      return {
        alive: true,
        message: "Process is alive and responsive",
      };
    } catch (error) {
      return {
        alive: false,
        message: `Liveness check failed: ${error.message}`,
      };
    }
  }
}
