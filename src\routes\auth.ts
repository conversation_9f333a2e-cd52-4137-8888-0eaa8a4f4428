import { Router } from "express";
import { authRateLimiter } from "@/middleware/rateLimiter";
import { registerValidation, loginValidation, refreshTokenValidation } from "@/middleware/validation";
import { authenticate } from "@/middleware/auth";
import * as authController from "@/controllers/authController";

const router = Router();

// Apply rate limiting to all auth routes
router.use(authRateLimiter);

// Registration
router.post("/register", registerValidation, authController.register);

// Login
router.post("/login", loginValidation, authController.login);

// Logout
router.post("/logout", authenticate, authController.logout);

// Refresh token
router.post("/refresh", refreshTokenValidation, authController.refreshToken);

// Get current user
router.get("/me", authenticate, authController.getCurrentUser);

// Verify email (for future implementation)
router.get("/verify/:token", authController.verifyEmail);

// Request password reset (for future implementation)
router.post("/forgot-password", authController.forgotPassword);

// Reset password (for future implementation)
router.post("/reset-password/:token", authController.resetPassword);

export default router;
