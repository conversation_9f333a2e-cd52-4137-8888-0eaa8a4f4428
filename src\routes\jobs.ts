import { Router } from "express";
import { authenticate, requireApproved, require<PERSON>lum<PERSON> } from "@/middleware/auth";
import {
  createJobValidation,
  paginationValidation,
  idValidation,
  jobApplicationValidation,
  applicationStatusValidation,
} from "@/middleware/validation";
import * as jobController from "@/controllers/jobController";

const router = Router();

// Public routes (no authentication required)
router.get("/", paginationValidation, jobController.getJobs);
router.get("/:id", idValidation, jobController.getJobById);

// Protected routes (authentication required)
router.use(authenticate);
router.use(requireApproved);

// Alumni-only routes
router.post("/", requireAlumni, createJobValidation, jobController.createJob);
router.put("/:id", requireAlumni, idValidation, createJobValidation, jobController.updateJob);
router.delete("/:id", requireAlumni, idValidation, jobController.deleteJob);
router.get("/my/posted", requireAlumni, paginationValidation, jobController.getMyPostedJobs);

// Job application routes (students and alumni can apply)
router.post("/:id/apply", idValidation, jobApplicationValidation, jobController.applyToJob);
router.get("/:id/applications", requireAlumni, idValidation, paginationValidation, jobController.getJobApplications);
router.get("/my/applications", paginationValidation, jobController.getMyApplications);
router.put(
  "/applications/:id/status",
  requireAlumni,
  idValidation,
  applicationStatusValidation,
  jobController.updateApplicationStatus
);

export default router;
