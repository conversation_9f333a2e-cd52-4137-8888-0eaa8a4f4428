import Redis from 'ioredis';

// Redis configuration
const getRedisConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    
    // Connection pool settings
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: true,
    maxLoadingTimeout: 5000,
    
    // Connection timeout
    connectTimeout: 10000,
    commandTimeout: 5000,
    
    // Reconnection settings
    lazyConnect: true,
    keepAlive: 30000,
    
    // Production optimizations
    ...(isProduction && {
      family: 4,
      keepAlive: 30000,
      dropBufferSupport: true,
    }),
  };
};

// Create Redis instances
export const redis = new Redis(getRedisConfig());
export const redisSubscriber = new Redis(getRedisConfig());
export const redisPublisher = new Redis(getRedisConfig());

// Redis health check
export const checkRedisHealth = async (): Promise<boolean> => {
  try {
    const result = await redis.ping();
    return result === 'PONG';
  } catch (error) {
    console.error('Redis health check failed:', error);
    return false;
  }
};

// Cache utility functions
export class CacheService {
  private static readonly DEFAULT_TTL = 3600; // 1 hour in seconds
  
  // Get cached data
  static async get<T>(key: string): Promise<T | null> {
    try {
      const cached = await redis.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }
  
  // Set cached data
  static async set(key: string, data: any, ttl: number = this.DEFAULT_TTL): Promise<boolean> {
    try {
      await redis.setex(key, ttl, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  }
  
  // Delete cached data
  static async del(key: string): Promise<boolean> {
    try {
      await redis.del(key);
      return true;
    } catch (error) {
      console.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  }
  
  // Delete multiple keys by pattern
  static async delPattern(pattern: string): Promise<number> {
    try {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        return await redis.del(...keys);
      }
      return 0;
    } catch (error) {
      console.error(`Cache delete pattern error for ${pattern}:`, error);
      return 0;
    }
  }
  
  // Check if key exists
  static async exists(key: string): Promise<boolean> {
    try {
      const result = await redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error(`Cache exists error for key ${key}:`, error);
      return false;
    }
  }
  
  // Increment counter
  static async incr(key: string, ttl?: number): Promise<number> {
    try {
      const result = await redis.incr(key);
      if (ttl && result === 1) {
        await redis.expire(key, ttl);
      }
      return result;
    } catch (error) {
      console.error(`Cache increment error for key ${key}:`, error);
      return 0;
    }
  }
  
  // Get or set pattern (cache-aside)
  static async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl: number = this.DEFAULT_TTL
  ): Promise<T | null> {
    try {
      // Try to get from cache first
      const cached = await this.get<T>(key);
      if (cached !== null) {
        return cached;
      }
      
      // If not in cache, fetch data
      const data = await fetchFunction();
      if (data !== null && data !== undefined) {
        await this.set(key, data, ttl);
      }
      
      return data;
    } catch (error) {
      console.error(`Cache getOrSet error for key ${key}:`, error);
      // If cache fails, still try to fetch data
      try {
        return await fetchFunction();
      } catch (fetchError) {
        console.error(`Fetch function error for key ${key}:`, fetchError);
        return null;
      }
    }
  }
}

// Cache key generators
export const CacheKeys = {
  user: (userId: string) => `user:${userId}`,
  userProfile: (userId: string) => `user:profile:${userId}`,
  userConnections: (userId: string) => `user:connections:${userId}`,
  job: (jobId: string) => `job:${jobId}`,
  jobsList: (page: number, filters: string) => `jobs:list:${page}:${filters}`,
  event: (eventId: string) => `event:${eventId}`,
  eventsList: (page: number, filters: string) => `events:list:${page}:${filters}`,
  post: (postId: string) => `post:${postId}`,
  postsList: (page: number, filters: string) => `posts:list:${page}:${filters}`,
  notifications: (userId: string) => `notifications:${userId}`,
  dashboardMetrics: () => 'dashboard:metrics',
  userSearch: (query: string, page: number) => `search:users:${query}:${page}`,
  rateLimitUser: (userId: string) => `ratelimit:user:${userId}`,
  rateLimitIP: (ip: string) => `ratelimit:ip:${ip}`,
  session: (sessionId: string) => `session:${sessionId}`,
};

// Redis connection event handlers
redis.on('connect', () => {
  console.log('✅ Redis connected successfully');
});

redis.on('ready', () => {
  console.log('✅ Redis ready for operations');
});

redis.on('error', (error) => {
  console.error('❌ Redis connection error:', error);
});

redis.on('close', () => {
  console.log('⚠️ Redis connection closed');
});

redis.on('reconnecting', () => {
  console.log('🔄 Redis reconnecting...');
});

// Graceful shutdown
const gracefulRedisShutdown = async (signal: string) => {
  console.log(`Received ${signal}, closing Redis connections...`);
  
  try {
    await Promise.all([
      redis.quit(),
      redisSubscriber.quit(),
      redisPublisher.quit(),
    ]);
    console.log('Redis connections closed successfully');
  } catch (error) {
    console.error('Error during Redis shutdown:', error);
  }
};

process.on('SIGTERM', () => gracefulRedisShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulRedisShutdown('SIGINT'));

export default redis;
