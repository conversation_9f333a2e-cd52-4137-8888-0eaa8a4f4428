import { PrismaClient, Prisma } from "@prisma/client";
import { Logger } from "@/services/loggerService";

declare global {
  var __prisma: PrismaClient | undefined;
  var __prismaReadReplica: PrismaClient | undefined;
}

// Database configuration for optimal performance
const getDatabaseConfig = (): Prisma.PrismaClientOptions => {
  const isProduction = process.env.NODE_ENV === "production";

  return {
    // Logging configuration
    log: isProduction
      ? [
          { level: "error", emit: "event" },
          { level: "warn", emit: "event" },
        ]
      : [
          { level: "query", emit: "event" },
          { level: "error", emit: "event" },
          { level: "warn", emit: "event" },
          { level: "info", emit: "event" },
        ],

    // Connection pool configuration (via DATABASE_URL parameters)
    // These are set in the connection string, but documented here:
    // ?connection_limit=20&pool_timeout=20&socket_timeout=60

    // Error formatting
    errorFormat: isProduction ? "minimal" : "pretty",
  };
};

// Read replica configuration (if available)
const getReadReplicaConfig = (): Prisma.PrismaClientOptions => {
  const config = getDatabaseConfig();
  return {
    ...config,
    // Read replicas typically have different connection strings
    datasources: {
      db: {
        url: process.env.DATABASE_READ_REPLICA_URL || process.env.DATABASE_URL,
      },
    },
  };
};

// Create Prisma clients with enhanced configuration
const createPrismaClient = (config: Prisma.PrismaClientOptions) => {
  const client = new PrismaClient(config);

  // Note: Prisma event listeners have type issues in current version
  // We'll implement database monitoring through middleware instead

  return client;
};

// Main database client
const prisma = globalThis.__prisma || createPrismaClient(getDatabaseConfig());

// Read replica client (if configured)
const prismaReadReplica =
  globalThis.__prismaReadReplica ||
  (process.env.DATABASE_READ_REPLICA_URL ? createPrismaClient(getReadReplicaConfig()) : prisma);

if (process.env.NODE_ENV === "development") {
  globalThis.__prisma = prisma;
  globalThis.__prismaReadReplica = prismaReadReplica;
}

// Database health check function
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    Logger.error("Database health check failed", error);
    return false;
  }
};

// Read replica health check
export const checkReadReplicaHealth = async (): Promise<boolean> => {
  if (prismaReadReplica === prisma) {
    return true; // No separate read replica
  }

  try {
    await prismaReadReplica.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    Logger.error("Read replica health check failed", error);
    return false;
  }
};

// Database metrics collection with performance monitoring
export const getDatabaseMetrics = async () => {
  const startTime = Date.now();

  try {
    const [userCount, jobCount, eventCount, messageCount, connectionInfo] = await Promise.all([
      prisma.user.count(),
      prisma.job.count(),
      prisma.event.count(),
      prisma.message.count(),
      getConnectionInfo(),
    ]);

    const queryTime = Date.now() - startTime;

    Logger.performance("Database metrics collection", queryTime);

    return {
      users: userCount,
      jobs: jobCount,
      events: eventCount,
      messages: messageCount,
      queryTime: `${queryTime}ms`,
      connection: connectionInfo,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    Logger.error("Failed to collect database metrics", error);
    return null;
  }
};

// Enhanced connection monitoring
export const getConnectionInfo = async () => {
  try {
    // Get basic connection info
    const startTime = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const pingTime = Date.now() - startTime;

    return {
      status: "connected",
      pingTime: `${pingTime}ms`,
      hasReadReplica: prismaReadReplica !== prisma,
      timestamp: new Date().toISOString(),
      // Note: Prisma doesn't expose detailed connection pool info
      // In production, you might want to use a custom connection pool
      // or monitor at the database level
    };
  } catch (error) {
    Logger.error("Failed to get connection info", error);
    return {
      status: "error",
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
    };
  }
};

// Database query performance wrapper
export const withQueryPerformanceMonitoring = async <T>(operation: string, queryFn: () => Promise<T>): Promise<T> => {
  const startTime = Date.now();

  try {
    const result = await queryFn();
    const duration = Date.now() - startTime;

    Logger.performance(`Database operation: ${operation}`, duration);

    // Log slow queries
    if (duration > 1000) {
      Logger.warn(`Slow database query detected: ${operation}`, {
        duration: `${duration}ms`,
      });
    }

    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error(`Database operation failed: ${operation}`, {
      error: error instanceof Error ? error.message : String(error),
      duration: `${duration}ms`,
    });
    throw error;
  }
};

// Read/Write client selector for better load distribution
export const getReadClient = () => prismaReadReplica;
export const getWriteClient = () => prisma;

// Enhanced graceful shutdown with connection cleanup
export const gracefulDatabaseShutdown = async (): Promise<void> => {
  Logger.info("Shutting down database connections...");

  try {
    await Promise.all([
      prisma.$disconnect(),
      prismaReadReplica !== prisma ? prismaReadReplica.$disconnect() : Promise.resolve(),
    ]);

    Logger.info("Database connections closed successfully");
  } catch (error) {
    Logger.error("Error during database shutdown", error);
    throw error;
  }
};

// Connection pool status monitoring
export const getConnectionPoolStatus = () => {
  // Note: Prisma doesn't expose connection pool metrics directly
  // This is a placeholder for future enhancement with custom connection pooling
  return {
    active: "unknown",
    idle: "unknown",
    total: "unknown",
    waiting: "unknown",
    note: "Prisma manages connection pooling internally. For detailed metrics, consider using a custom connection pool or database-level monitoring.",
  };
};

export { prisma };
const gracefulShutdown = async (signal: string) => {
  console.log(`Received ${signal}, starting graceful shutdown...`);

  try {
    await prisma.$disconnect();
    console.log("Database connections closed successfully");
  } catch (error) {
    console.error("Error during database shutdown:", error);
  }

  process.exit(0);
};

// Handle various shutdown signals
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));
process.on("beforeExit", async () => {
  await prisma.$disconnect();
});
