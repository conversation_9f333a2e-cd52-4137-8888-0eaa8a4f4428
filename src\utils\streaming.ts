import { Response } from "express";
import { Readable, Transform } from "stream";
import { Logger } from "@/services/loggerService";

// Streaming configuration
const STREAMING_CONFIG = {
  chunkSize: parseInt(process.env.STREAMING_CHUNK_SIZE || "1000"),
  maxConcurrentStreams: parseInt(process.env.MAX_CONCURRENT_STREAMS || "10"),
  streamTimeout: parseInt(process.env.STREAM_TIMEOUT || "30000"), // 30 seconds
};

// Active streams tracking
let activeStreams = 0;

// JSON streaming utility
export class JSONStreamer {
  private response: Response;
  private isFirstItem: boolean = true;
  private streamStarted: boolean = false;
  private streamEnded: boolean = false;

  constructor(response: Response) {
    this.response = response;
  }

  // Start the JSON array stream
  start(): void {
    if (this.streamStarted) {
      throw new Error("Stream already started");
    }

    if (activeStreams >= STREAMING_CONFIG.maxConcurrentStreams) {
      throw new Error("Maximum concurrent streams reached");
    }

    activeStreams++;
    this.streamStarted = true;

    // Set appropriate headers
    this.response.setHeader("Content-Type", "application/json");
    this.response.setHeader("Transfer-Encoding", "chunked");
    this.response.setHeader("X-Streaming", "true");

    // Start JSON array
    this.response.write("[");

    // Set timeout
    const timeout = setTimeout(() => {
      if (!this.streamEnded) {
        Logger.warn("Stream timeout reached", {
          timeout: STREAMING_CONFIG.streamTimeout,
        });
        this.end();
      }
    }, STREAMING_CONFIG.streamTimeout);

    // Clean up timeout when stream ends
    this.response.on("finish", () => {
      clearTimeout(timeout);
      activeStreams--;
    });

    this.response.on("error", (error) => {
      clearTimeout(timeout);
      activeStreams--;
      Logger.error("Stream error", error);
    });
  }

  // Write an item to the stream
  writeItem(item: any): void {
    if (!this.streamStarted) {
      throw new Error("Stream not started");
    }

    if (this.streamEnded) {
      throw new Error("Stream already ended");
    }

    try {
      const jsonItem = JSON.stringify(item);
      
      if (!this.isFirstItem) {
        this.response.write(",");
      }
      
      this.response.write(jsonItem);
      this.isFirstItem = false;
    } catch (error) {
      Logger.error("Failed to write stream item", {
        error: error instanceof Error ? error.message : String(error),
        item: typeof item,
      });
      throw error;
    }
  }

  // Write multiple items
  writeItems(items: any[]): void {
    for (const item of items) {
      this.writeItem(item);
    }
  }

  // End the stream
  end(): void {
    if (!this.streamStarted) {
      throw new Error("Stream not started");
    }

    if (this.streamEnded) {
      return;
    }

    this.streamEnded = true;
    this.response.write("]");
    this.response.end();
  }

  // Write error and end stream
  error(error: Error, statusCode: number = 500): void {
    if (this.streamEnded) {
      return;
    }

    try {
      if (!this.streamStarted) {
        this.response.status(statusCode).json({
          error: error.message,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // If stream started, we need to close the array and add error info
      this.response.write("],\"error\":");
      this.response.write(JSON.stringify({
        message: error.message,
        timestamp: new Date().toISOString(),
      }));
      this.response.write("}");
      this.response.end();
    } catch (writeError) {
      Logger.error("Failed to write stream error", writeError);
      this.response.end();
    }
  }
}

// CSV streaming utility
export class CSVStreamer {
  private response: Response;
  private headers: string[] = [];
  private headersSent: boolean = false;
  private streamStarted: boolean = false;
  private streamEnded: boolean = false;

  constructor(response: Response, headers: string[] = []) {
    this.response = response;
    this.headers = headers;
  }

  // Start the CSV stream
  start(): void {
    if (this.streamStarted) {
      throw new Error("Stream already started");
    }

    if (activeStreams >= STREAMING_CONFIG.maxConcurrentStreams) {
      throw new Error("Maximum concurrent streams reached");
    }

    activeStreams++;
    this.streamStarted = true;

    // Set appropriate headers
    this.response.setHeader("Content-Type", "text/csv");
    this.response.setHeader("Transfer-Encoding", "chunked");
    this.response.setHeader("Content-Disposition", "attachment; filename=export.csv");
    this.response.setHeader("X-Streaming", "true");

    // Write CSV headers if provided
    if (this.headers.length > 0) {
      this.writeHeaders();
    }

    // Set timeout and cleanup
    const timeout = setTimeout(() => {
      if (!this.streamEnded) {
        this.end();
      }
    }, STREAMING_CONFIG.streamTimeout);

    this.response.on("finish", () => {
      clearTimeout(timeout);
      activeStreams--;
    });

    this.response.on("error", (error) => {
      clearTimeout(timeout);
      activeStreams--;
      Logger.error("CSV stream error", error);
    });
  }

  // Write CSV headers
  private writeHeaders(): void {
    if (this.headersSent) {
      return;
    }

    const csvHeaders = this.headers.map(header => this.escapeCsvField(header)).join(",");
    this.response.write(csvHeaders + "\n");
    this.headersSent = true;
  }

  // Write a row to the CSV
  writeRow(row: any[] | Record<string, any>): void {
    if (!this.streamStarted) {
      throw new Error("Stream not started");
    }

    if (this.streamEnded) {
      throw new Error("Stream already ended");
    }

    try {
      let values: any[];

      if (Array.isArray(row)) {
        values = row;
      } else {
        // If headers are defined, use them to order the values
        if (this.headers.length > 0) {
          values = this.headers.map(header => row[header] ?? "");
        } else {
          // Auto-detect headers from first row
          if (!this.headersSent) {
            this.headers = Object.keys(row);
            this.writeHeaders();
          }
          values = Object.values(row);
        }
      }

      const csvRow = values.map(value => this.escapeCsvField(value)).join(",");
      this.response.write(csvRow + "\n");
    } catch (error) {
      Logger.error("Failed to write CSV row", {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  // Write multiple rows
  writeRows(rows: any[]): void {
    for (const row of rows) {
      this.writeRow(row);
    }
  }

  // Escape CSV field
  private escapeCsvField(value: any): string {
    if (value === null || value === undefined) {
      return "";
    }

    const stringValue = String(value);
    
    // If the value contains comma, quote, or newline, wrap in quotes and escape quotes
    if (stringValue.includes(",") || stringValue.includes('"') || stringValue.includes("\n")) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }

    return stringValue;
  }

  // End the stream
  end(): void {
    if (!this.streamStarted) {
      throw new Error("Stream not started");
    }

    if (this.streamEnded) {
      return;
    }

    this.streamEnded = true;
    this.response.end();
  }

  // Handle error
  error(error: Error): void {
    if (this.streamEnded) {
      return;
    }

    Logger.error("CSV stream error", error);
    this.response.status(500).end();
  }
}

// Database result streaming utility
export async function streamDatabaseResults<T>(
  response: Response,
  queryFn: (offset: number, limit: number) => Promise<T[]>,
  totalCountFn: () => Promise<number>,
  format: "json" | "csv" = "json",
  csvHeaders?: string[]
): Promise<void> {
  try {
    const total = await totalCountFn();
    
    if (total === 0) {
      if (format === "json") {
        response.json([]);
      } else {
        response.setHeader("Content-Type", "text/csv");
        response.end();
      }
      return;
    }

    // Initialize streamer
    let streamer: JSONStreamer | CSVStreamer;
    
    if (format === "json") {
      streamer = new JSONStreamer(response);
    } else {
      streamer = new CSVStreamer(response, csvHeaders);
    }

    streamer.start();

    // Stream data in chunks
    let offset = 0;
    const chunkSize = STREAMING_CONFIG.chunkSize;

    while (offset < total) {
      try {
        const results = await queryFn(offset, chunkSize);
        
        if (results.length === 0) {
          break;
        }

        if (format === "json") {
          (streamer as JSONStreamer).writeItems(results);
        } else {
          (streamer as CSVStreamer).writeRows(results);
        }

        offset += results.length;

        // Add small delay to prevent overwhelming the database
        if (offset < total) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      } catch (error) {
        Logger.error("Error streaming database chunk", {
          error: error instanceof Error ? error.message : String(error),
          offset,
          chunkSize,
        });
        throw error;
      }
    }

    streamer.end();
    
    Logger.info("Database streaming completed", {
      total,
      format,
      chunks: Math.ceil(total / chunkSize),
    });
  } catch (error) {
    Logger.error("Database streaming failed", {
      error: error instanceof Error ? error.message : String(error),
      format,
    });
    
    if (!response.headersSent) {
      response.status(500).json({
        error: "Streaming failed",
        message: error instanceof Error ? error.message : String(error),
      });
    }
  }
}

// Transform stream for data processing
export class DataTransformStream extends Transform {
  private transformFn: (chunk: any) => any;

  constructor(transformFn: (chunk: any) => any) {
    super({ objectMode: true });
    this.transformFn = transformFn;
  }

  _transform(chunk: any, encoding: string, callback: Function) {
    try {
      const transformed = this.transformFn(chunk);
      callback(null, transformed);
    } catch (error) {
      callback(error);
    }
  }
}

// Get streaming statistics
export function getStreamingStats() {
  return {
    activeStreams,
    maxConcurrentStreams: STREAMING_CONFIG.maxConcurrentStreams,
    chunkSize: STREAMING_CONFIG.chunkSize,
    streamTimeout: STREAMING_CONFIG.streamTimeout,
  };
}
