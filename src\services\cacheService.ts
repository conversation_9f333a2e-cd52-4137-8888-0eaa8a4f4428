import { redis, CacheService as RedisCacheService, Cache<PERSON>ey<PERSON> } from "@/config/redis";
import { Logger } from "@/services/loggerService";

// In-memory cache configuration
const MEMORY_CACHE_CONFIG = {
  maxSize: parseInt(process.env.MEMORY_CACHE_MAX_SIZE || "1000"),
  defaultTTL: parseInt(process.env.MEMORY_CACHE_TTL || "300"), // 5 minutes
  cleanupInterval: parseInt(process.env.MEMORY_CACHE_CLEANUP_INTERVAL || "60000"), // 1 minute
};

// Cache entry interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  hits: number;
}

// Multi-level cache implementation
export class AdvancedCacheService {
  private static memoryCache = new Map<string, CacheEntry<any>>();
  private static cacheStats = {
    memoryHits: 0,
    memoryMisses: 0,
    redisHits: 0,
    redisMisses: 0,
    totalRequests: 0,
  };

  // Initialize cache service
  static init() {
    // Start cleanup interval for memory cache
    setInterval(() => {
      this.cleanupMemoryCache();
    }, MEMORY_CACHE_CONFIG.cleanupInterval);

    Logger.info("Advanced cache service initialized", {
      memoryMaxSize: MEMORY_CACHE_CONFIG.maxSize,
      memoryTTL: MEMORY_CACHE_CONFIG.defaultTTL,
    });
  }

  // Get data with multi-level caching (L1: Memory, L2: Redis)
  static async get<T>(key: string): Promise<T | null> {
    this.cacheStats.totalRequests++;

    // L1 Cache: Check memory cache first
    const memoryResult = this.getFromMemory<T>(key);
    if (memoryResult !== null) {
      this.cacheStats.memoryHits++;
      Logger.cache(`Memory cache hit for key: ${key}`);
      return memoryResult;
    }
    this.cacheStats.memoryMisses++;

    // L2 Cache: Check Redis cache
    try {
      const redisResult = await RedisCacheService.get<T>(key);
      if (redisResult !== null) {
        this.cacheStats.redisHits++;
        Logger.cache(`Redis cache hit for key: ${key}`);
        
        // Store in memory cache for faster future access
        this.setInMemory(key, redisResult, MEMORY_CACHE_CONFIG.defaultTTL);
        return redisResult;
      }
      this.cacheStats.redisMisses++;
    } catch (error) {
      Logger.error("Redis cache get error", { key, error });
    }

    Logger.cache(`Cache miss for key: ${key}`);
    return null;
  }

  // Set data in both cache levels
  static async set<T>(key: string, data: T, ttl: number = 3600): Promise<boolean> {
    try {
      // Set in memory cache (shorter TTL for memory efficiency)
      const memoryTTL = Math.min(ttl, MEMORY_CACHE_CONFIG.defaultTTL);
      this.setInMemory(key, data, memoryTTL);

      // Set in Redis cache
      const redisSuccess = await RedisCacheService.set(key, data, ttl);
      
      Logger.cache(`Cache set for key: ${key}`, key, {
        memoryTTL,
        redisTTL: ttl,
        redisSuccess,
      });

      return redisSuccess;
    } catch (error) {
      Logger.error("Cache set error", { key, error });
      return false;
    }
  }

  // Delete from both cache levels
  static async delete(key: string): Promise<boolean> {
    try {
      // Delete from memory cache
      this.memoryCache.delete(key);

      // Delete from Redis cache
      const redisSuccess = await RedisCacheService.delete(key);
      
      Logger.cache(`Cache delete for key: ${key}`, key, { redisSuccess });
      return redisSuccess;
    } catch (error) {
      Logger.error("Cache delete error", { key, error });
      return false;
    }
  }

  // Get or set pattern with multi-level caching
  static async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl: number = 3600
  ): Promise<T | null> {
    // Try to get from cache first
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // If not in cache, fetch data
    try {
      const data = await fetchFunction();
      if (data !== null && data !== undefined) {
        await this.set(key, data, ttl);
      }
      return data;
    } catch (error) {
      Logger.error("Cache getOrSet fetch error", { key, error });
      return null;
    }
  }

  // Batch operations
  static async mget<T>(keys: string[]): Promise<Map<string, T | null>> {
    const results = new Map<string, T | null>();
    
    // Check memory cache first
    const memoryMisses: string[] = [];
    for (const key of keys) {
      const memoryResult = this.getFromMemory<T>(key);
      if (memoryResult !== null) {
        results.set(key, memoryResult);
        this.cacheStats.memoryHits++;
      } else {
        memoryMisses.push(key);
        this.cacheStats.memoryMisses++;
      }
    }

    // Check Redis for memory misses
    if (memoryMisses.length > 0) {
      try {
        const redisResults = await RedisCacheService.mget<T>(memoryMisses);
        for (const [key, value] of redisResults) {
          results.set(key, value);
          if (value !== null) {
            this.cacheStats.redisHits++;
            // Store in memory cache
            this.setInMemory(key, value, MEMORY_CACHE_CONFIG.defaultTTL);
          } else {
            this.cacheStats.redisMisses++;
          }
        }
      } catch (error) {
        Logger.error("Redis mget error", { keys: memoryMisses, error });
        // Set null for failed keys
        for (const key of memoryMisses) {
          if (!results.has(key)) {
            results.set(key, null);
          }
        }
      }
    }

    this.cacheStats.totalRequests += keys.length;
    return results;
  }

  // Cache warming - preload frequently accessed data
  static async warmCache(warmingConfig: Array<{
    key: string;
    fetchFunction: () => Promise<any>;
    ttl?: number;
  }>): Promise<void> {
    Logger.info(`Starting cache warming for ${warmingConfig.length} keys`);
    
    const startTime = Date.now();
    const results = await Promise.allSettled(
      warmingConfig.map(async ({ key, fetchFunction, ttl = 3600 }) => {
        try {
          const data = await fetchFunction();
          await this.set(key, data, ttl);
          return { key, success: true };
        } catch (error) {
          Logger.error(`Cache warming failed for key: ${key}`, error);
          return { key, success: false, error };
        }
      })
    );

    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    const duration = Date.now() - startTime;
    
    Logger.performance("Cache warming completed", duration, {
      total: warmingConfig.length,
      successful,
      failed: warmingConfig.length - successful,
    });
  }

  // Cache invalidation patterns
  static async invalidatePattern(pattern: string): Promise<number> {
    try {
      // Invalidate from memory cache
      let memoryInvalidated = 0;
      for (const key of this.memoryCache.keys()) {
        if (key.includes(pattern)) {
          this.memoryCache.delete(key);
          memoryInvalidated++;
        }
      }

      // Invalidate from Redis cache
      const redisInvalidated = await RedisCacheService.deletePattern(pattern);
      
      Logger.cache(`Cache pattern invalidation: ${pattern}`, pattern, {
        memoryInvalidated,
        redisInvalidated,
      });

      return memoryInvalidated + redisInvalidated;
    } catch (error) {
      Logger.error("Cache pattern invalidation error", { pattern, error });
      return 0;
    }
  }

  // Memory cache operations
  private static getFromMemory<T>(key: string): T | null {
    const entry = this.memoryCache.get(key);
    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl * 1000) {
      this.memoryCache.delete(key);
      return null;
    }

    // Update hit count
    entry.hits++;
    return entry.data;
  }

  private static setInMemory<T>(key: string, data: T, ttl: number): void {
    // Check cache size limit
    if (this.memoryCache.size >= MEMORY_CACHE_CONFIG.maxSize) {
      this.evictLeastUsed();
    }

    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0,
    });
  }

  // LRU eviction for memory cache
  private static evictLeastUsed(): void {
    let leastUsedKey = '';
    let leastHits = Infinity;
    let oldestTime = Infinity;

    for (const [key, entry] of this.memoryCache) {
      if (entry.hits < leastHits || (entry.hits === leastHits && entry.timestamp < oldestTime)) {
        leastUsedKey = key;
        leastHits = entry.hits;
        oldestTime = entry.timestamp;
      }
    }

    if (leastUsedKey) {
      this.memoryCache.delete(leastUsedKey);
      Logger.cache(`Evicted least used cache entry: ${leastUsedKey}`);
    }
  }

  // Cleanup expired entries from memory cache
  private static cleanupMemoryCache(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.memoryCache) {
      if (now - entry.timestamp > entry.ttl * 1000) {
        this.memoryCache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      Logger.cache(`Cleaned up ${cleaned} expired memory cache entries`);
    }
  }

  // Get cache statistics
  static getStats() {
    const memorySize = this.memoryCache.size;
    const memoryHitRate = this.cacheStats.totalRequests > 0 
      ? (this.cacheStats.memoryHits / this.cacheStats.totalRequests * 100).toFixed(2)
      : '0.00';
    const redisHitRate = this.cacheStats.totalRequests > 0
      ? (this.cacheStats.redisHits / this.cacheStats.totalRequests * 100).toFixed(2)
      : '0.00';
    const overallHitRate = this.cacheStats.totalRequests > 0
      ? ((this.cacheStats.memoryHits + this.cacheStats.redisHits) / this.cacheStats.totalRequests * 100).toFixed(2)
      : '0.00';

    return {
      memory: {
        size: memorySize,
        maxSize: MEMORY_CACHE_CONFIG.maxSize,
        hitRate: `${memoryHitRate}%`,
        hits: this.cacheStats.memoryHits,
        misses: this.cacheStats.memoryMisses,
      },
      redis: {
        hitRate: `${redisHitRate}%`,
        hits: this.cacheStats.redisHits,
        misses: this.cacheStats.redisMisses,
      },
      overall: {
        hitRate: `${overallHitRate}%`,
        totalRequests: this.cacheStats.totalRequests,
      },
      timestamp: new Date().toISOString(),
    };
  }

  // Reset cache statistics
  static resetStats(): void {
    this.cacheStats = {
      memoryHits: 0,
      memoryMisses: 0,
      redisHits: 0,
      redisMisses: 0,
      totalRequests: 0,
    };
    Logger.info("Cache statistics reset");
  }

  // Clear all caches
  static async clearAll(): Promise<void> {
    this.memoryCache.clear();
    await RedisCacheService.clear();
    this.resetStats();
    Logger.info("All caches cleared");
  }
}

// Initialize the cache service
AdvancedCacheService.init();
