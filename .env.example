# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/alumni_portal?schema=public&connection_limit=20&pool_timeout=20&socket_timeout=60"
DATABASE_READ_REPLICA_URL="postgresql://username:password@localhost:5432/alumni_portal_read?schema=public&connection_limit=15&pool_timeout=20&socket_timeout=60"

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-token-secret-here
JWT_REFRESH_EXPIRES_IN=30d

# Bcrypt Configuration
BCRYPT_SALT_ROUNDS=12

# Cloudinary Configuration (for file uploads)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Alumni Portal"

# WebSocket Configuration
WS_PORT=3001

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# CORS Configuration
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Clustering Configuration
CLUSTER_ENABLED=true
CLUSTER_WORKERS=4
CLUSTER_RESTART_DELAY=1000
CLUSTER_MAX_RESTARTS=5
CLUSTER_SHUTDOWN_TIMEOUT=10000

# Redis Configuration (for caching and queues)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Memory Cache Configuration
MEMORY_CACHE_MAX_SIZE=1000
MEMORY_CACHE_TTL=300
MEMORY_CACHE_CLEANUP_INTERVAL=60000

# Queue Configuration
QUEUE_KEEP_COMPLETED=50
QUEUE_KEEP_FAILED=100
QUEUE_MAX_ATTEMPTS=3
QUEUE_BACKOFF_DELAY=2000
QUEUE_STALLED_INTERVAL=30000
QUEUE_MAX_STALLED=1

# Response Optimization
MINIFY_JSON=true
ENABLE_FIELD_FILTERING=true
ENABLE_TRANSFORMATION=true
MAX_RESPONSE_SIZE=10485760
ENABLE_CACHE_HEADERS=true

# Streaming Configuration
STREAMING_CHUNK_SIZE=1000
MAX_CONCURRENT_STREAMS=10
STREAM_TIMEOUT=30000

# Session Management
SESSION_TTL=86400
REFRESH_TOKEN_TTL=604800
MAX_SESSIONS_PER_USER=5
SESSION_CLEANUP_INTERVAL=3600000
ENABLE_SESSION_CLUSTERING=true
SESSION_PREFIX=session:
REFRESH_PREFIX=refresh:
USER_SESSIONS_PREFIX=user_sessions:
ACCESS_TOKEN_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d
