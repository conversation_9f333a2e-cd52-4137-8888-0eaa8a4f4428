import Bull from 'bull';
import { redis } from '@/config/redis';
import { sendEmail } from './emailService';
import { createNotification } from './notificationService';
import { uploadToCloudinary } from './cloudinaryService';

// Queue configuration
const queueConfig = {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
  },
  defaultJobOptions: {
    removeOnComplete: 10, // Keep only 10 completed jobs
    removeOnFail: 50, // Keep 50 failed jobs for debugging
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
};

// Create queues
export const emailQueue = new Bull('email processing', queueConfig);
export const notificationQueue = new Bull('notification processing', queueConfig);
export const fileProcessingQueue = new Bull('file processing', queueConfig);
export const analyticsQueue = new Bull('analytics processing', queueConfig);

// Email queue processors
emailQueue.process('send-email', async (job) => {
  const { to, subject, html, text } = job.data;
  
  try {
    await sendEmail({ to, subject, html, text });
    console.log(`✅ Email sent successfully to ${to}`);
    return { success: true, recipient: to };
  } catch (error) {
    console.error(`❌ Failed to send email to ${to}:`, error);
    throw error;
  }
});

emailQueue.process('send-bulk-email', async (job) => {
  const { recipients, subject, html, text } = job.data;
  const results = [];
  
  for (const recipient of recipients) {
    try {
      await sendEmail({ to: recipient, subject, html, text });
      results.push({ recipient, success: true });
      console.log(`✅ Bulk email sent to ${recipient}`);
    } catch (error) {
      results.push({ recipient, success: false, error: error.message });
      console.error(`❌ Failed to send bulk email to ${recipient}:`, error);
    }
    
    // Add small delay between emails to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  return results;
});

// Notification queue processors
notificationQueue.process('send-notification', async (job) => {
  const notificationData = job.data;
  
  try {
    const notification = await createNotification(notificationData);
    console.log(`✅ Notification created: ${notification.id}`);
    return { success: true, notificationId: notification.id };
  } catch (error) {
    console.error('❌ Failed to create notification:', error);
    throw error;
  }
});

notificationQueue.process('send-bulk-notifications', async (job) => {
  const { notifications } = job.data;
  const results = [];
  
  for (const notificationData of notifications) {
    try {
      const notification = await createNotification(notificationData);
      results.push({ success: true, notificationId: notification.id });
      console.log(`✅ Bulk notification created: ${notification.id}`);
    } catch (error) {
      results.push({ success: false, error: error.message });
      console.error('❌ Failed to create bulk notification:', error);
    }
  }
  
  return results;
});

// File processing queue processors
fileProcessingQueue.process('optimize-image', async (job) => {
  const { fileBuffer, options, userId } = job.data;
  
  try {
    const result = await uploadToCloudinary(fileBuffer, options);
    console.log(`✅ Image optimized for user ${userId}`);
    return result;
  } catch (error) {
    console.error(`❌ Failed to optimize image for user ${userId}:`, error);
    throw error;
  }
});

// Analytics queue processors
analyticsQueue.process('track-event', async (job) => {
  const { eventType, userId, data } = job.data;
  
  try {
    // Here you would implement your analytics tracking
    // For example, sending to Google Analytics, Mixpanel, etc.
    console.log(`📊 Analytics event tracked: ${eventType} for user ${userId}`);
    return { success: true, eventType, userId };
  } catch (error) {
    console.error(`❌ Failed to track analytics event:`, error);
    throw error;
  }
});

// Queue utility functions
export class QueueService {
  // Add email to queue
  static async sendEmail(emailData: {
    to: string;
    subject: string;
    html: string;
    text?: string;
  }, priority: number = 0) {
    return emailQueue.add('send-email', emailData, {
      priority,
      delay: 0,
    });
  }
  
  // Add bulk email to queue
  static async sendBulkEmail(emailData: {
    recipients: string[];
    subject: string;
    html: string;
    text?: string;
  }, priority: number = -5) {
    return emailQueue.add('send-bulk-email', emailData, {
      priority,
      delay: 0,
    });
  }
  
  // Add notification to queue
  static async sendNotification(notificationData: any, priority: number = 0) {
    return notificationQueue.add('send-notification', notificationData, {
      priority,
      delay: 0,
    });
  }
  
  // Add bulk notifications to queue
  static async sendBulkNotifications(notifications: any[], priority: number = -5) {
    return notificationQueue.add('send-bulk-notifications', { notifications }, {
      priority,
      delay: 0,
    });
  }
  
  // Add file processing to queue
  static async processFile(fileData: {
    fileBuffer: Buffer;
    options: any;
    userId: string;
  }, priority: number = 0) {
    return fileProcessingQueue.add('optimize-image', fileData, {
      priority,
      delay: 0,
    });
  }
  
  // Add analytics event to queue
  static async trackEvent(eventData: {
    eventType: string;
    userId: string;
    data: any;
  }, priority: number = -10) {
    return analyticsQueue.add('track-event', eventData, {
      priority,
      delay: 0,
    });
  }
  
  // Get queue statistics
  static async getQueueStats() {
    const [emailStats, notificationStats, fileStats, analyticsStats] = await Promise.all([
      emailQueue.getJobCounts(),
      notificationQueue.getJobCounts(),
      fileProcessingQueue.getJobCounts(),
      analyticsQueue.getJobCounts(),
    ]);
    
    return {
      email: emailStats,
      notifications: notificationStats,
      fileProcessing: fileStats,
      analytics: analyticsStats,
      timestamp: new Date().toISOString(),
    };
  }
  
  // Clean up completed jobs
  static async cleanupQueues() {
    await Promise.all([
      emailQueue.clean(24 * 60 * 60 * 1000, 'completed'), // Clean completed jobs older than 24 hours
      emailQueue.clean(7 * 24 * 60 * 60 * 1000, 'failed'), // Clean failed jobs older than 7 days
      notificationQueue.clean(24 * 60 * 60 * 1000, 'completed'),
      notificationQueue.clean(7 * 24 * 60 * 60 * 1000, 'failed'),
      fileProcessingQueue.clean(24 * 60 * 60 * 1000, 'completed'),
      fileProcessingQueue.clean(7 * 24 * 60 * 60 * 1000, 'failed'),
      analyticsQueue.clean(24 * 60 * 60 * 1000, 'completed'),
      analyticsQueue.clean(7 * 24 * 60 * 60 * 1000, 'failed'),
    ]);
    
    console.log('✅ Queue cleanup completed');
  }
}

// Queue event handlers
const setupQueueEventHandlers = (queue: Bull.Queue, queueName: string) => {
  queue.on('completed', (job) => {
    console.log(`✅ ${queueName} job ${job.id} completed`);
  });
  
  queue.on('failed', (job, err) => {
    console.error(`❌ ${queueName} job ${job.id} failed:`, err);
  });
  
  queue.on('stalled', (job) => {
    console.warn(`⚠️ ${queueName} job ${job.id} stalled`);
  });
};

// Setup event handlers for all queues
setupQueueEventHandlers(emailQueue, 'Email');
setupQueueEventHandlers(notificationQueue, 'Notification');
setupQueueEventHandlers(fileProcessingQueue, 'File Processing');
setupQueueEventHandlers(analyticsQueue, 'Analytics');

// Schedule periodic cleanup
setInterval(() => {
  QueueService.cleanupQueues().catch(console.error);
}, 60 * 60 * 1000); // Run every hour

// Graceful shutdown
const gracefulQueueShutdown = async (signal: string) => {
  console.log(`Received ${signal}, closing queues...`);
  
  try {
    await Promise.all([
      emailQueue.close(),
      notificationQueue.close(),
      fileProcessingQueue.close(),
      analyticsQueue.close(),
    ]);
    console.log('All queues closed successfully');
  } catch (error) {
    console.error('Error during queue shutdown:', error);
  }
};

process.on('SIGTERM', () => gracefulQueueShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulQueueShutdown('SIGINT'));

export default QueueService;
