import { Router } from "express";
import { MetricsService } from "@/services/metricsService";
import { AdvancedCacheService } from "@/services/cacheService";
import { AdvancedQueueService } from "@/services/queueService";
import { HealthService } from "@/services/healthService";
import { authenticate, authorize } from "@/middleware/auth";
import { UserRole } from "@prisma/client";

const router = Router();

// Middleware to protect metrics endpoints (admin only)
router.use(authenticate);
router.use(authorize(UserRole.ADMIN));

// Get comprehensive performance report
router.get("/performance", async (req, res, next) => {
  try {
    const report = await MetricsService.getPerformanceReport();
    res.json(report);
  } catch (error) {
    next(error);
  }
});

// Get real-time metrics (last 5 minutes)
router.get("/realtime", async (req, res, next) => {
  try {
    const metrics = MetricsService.getRealTimeMetrics();
    res.json({
      ...metrics,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

// Get metrics for specific time range
router.get("/range", async (req, res, next) => {
  try {
    const { start, end } = req.query;
    
    if (!start || !end) {
      return res.status(400).json({
        error: "Both 'start' and 'end' query parameters are required",
      });
    }

    const startTime = new Date(start as string).getTime();
    const endTime = new Date(end as string).getTime();

    if (isNaN(startTime) || isNaN(endTime)) {
      return res.status(400).json({
        error: "Invalid date format. Use ISO 8601 format.",
      });
    }

    const metrics = MetricsService.getMetricsInRange(startTime, endTime);
    res.json({
      ...metrics,
      period: {
        start: new Date(startTime).toISOString(),
        end: new Date(endTime).toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// Get metrics summary
router.get("/summary", async (req, res, next) => {
  try {
    const summary = MetricsService.getMetricsSummary();
    res.json(summary);
  } catch (error) {
    next(error);
  }
});

// Get cache statistics
router.get("/cache", async (req, res, next) => {
  try {
    const stats = AdvancedCacheService.getStats();
    res.json(stats);
  } catch (error) {
    next(error);
  }
});

// Get queue statistics
router.get("/queues", async (req, res, next) => {
  try {
    const stats = await AdvancedQueueService.getQueueStats();
    res.json(stats);
  } catch (error) {
    next(error);
  }
});

// Get queue health
router.get("/queues/health", async (req, res, next) => {
  try {
    const health = await AdvancedQueueService.getQueueHealth();
    res.json(health);
  } catch (error) {
    next(error);
  }
});

// Get comprehensive health status
router.get("/health", async (req, res, next) => {
  try {
    const health = await HealthService.getHealthStatus();
    const statusCode = health.status === "healthy" ? 200 : 
                      health.status === "degraded" ? 200 : 503;
    res.status(statusCode).json(health);
  } catch (error) {
    next(error);
  }
});

// Export metrics in Prometheus format
router.get("/prometheus", async (req, res, next) => {
  try {
    const metrics = MetricsService.exportPrometheusMetrics();
    res.set('Content-Type', 'text/plain');
    res.send(metrics);
  } catch (error) {
    next(error);
  }
});

// Record custom metric (POST endpoint for external systems)
router.post("/custom", async (req, res, next) => {
  try {
    const { name, value, unit = 'count', tags } = req.body;
    
    if (!name || value === undefined) {
      return res.status(400).json({
        error: "Both 'name' and 'value' are required",
      });
    }

    MetricsService.recordMetric(name, value, unit, tags);
    
    res.json({
      success: true,
      message: `Metric '${name}' recorded successfully`,
    });
  } catch (error) {
    next(error);
  }
});

// Queue management endpoints
router.post("/queues/:queueName/pause", async (req, res, next) => {
  try {
    const { queueName } = req.params;
    const result = await AdvancedQueueService.pauseQueue(queueName as any);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

router.post("/queues/:queueName/resume", async (req, res, next) => {
  try {
    const { queueName } = req.params;
    const result = await AdvancedQueueService.resumeQueue(queueName as any);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

router.post("/queues/:queueName/retry", async (req, res, next) => {
  try {
    const { queueName } = req.params;
    const { limit = 10 } = req.body;
    const result = await AdvancedQueueService.retryFailedJobs(queueName as any, limit);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

router.post("/queues/cleanup", async (req, res, next) => {
  try {
    const { olderThanHours = 24 } = req.body;
    const result = await AdvancedQueueService.cleanupJobs(olderThanHours);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Cache management endpoints
router.post("/cache/clear", async (req, res, next) => {
  try {
    await AdvancedCacheService.clearAll();
    res.json({
      success: true,
      message: "Cache cleared successfully",
    });
  } catch (error) {
    next(error);
  }
});

router.post("/cache/stats/reset", async (req, res, next) => {
  try {
    AdvancedCacheService.resetStats();
    res.json({
      success: true,
      message: "Cache statistics reset successfully",
    });
  } catch (error) {
    next(error);
  }
});

router.post("/cache/invalidate", async (req, res, next) => {
  try {
    const { pattern } = req.body;
    
    if (!pattern) {
      return res.status(400).json({
        error: "Pattern is required",
      });
    }

    const invalidated = await AdvancedCacheService.invalidatePattern(pattern);
    res.json({
      success: true,
      message: `Invalidated ${invalidated} cache entries`,
      pattern,
      invalidated,
    });
  } catch (error) {
    next(error);
  }
});

// System information endpoint
router.get("/system", async (req, res, next) => {
  try {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    res.json({
      process: {
        pid: process.pid,
        uptime: process.uptime(),
        version: process.version,
        platform: process.platform,
        arch: process.arch,
      },
      memory: {
        rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
        external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
        arrayBuffers: `${Math.round(memUsage.arrayBuffers / 1024 / 1024)}MB`,
      },
      cpu: {
        user: `${Math.round(cpuUsage.user / 1000)}ms`,
        system: `${Math.round(cpuUsage.system / 1000)}ms`,
      },
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

export default router;
