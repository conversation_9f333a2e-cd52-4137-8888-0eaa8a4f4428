import { v2 as cloudinary } from 'cloudinary';
import { Readable } from 'stream';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

interface UploadOptions {
  folder: string;
  public_id?: string;
  transformation?: any;
  resource_type?: 'image' | 'video' | 'raw' | 'auto';
}

interface UploadResult {
  public_id: string;
  secure_url: string;
  url: string;
  format: string;
  resource_type: string;
  bytes: number;
  width?: number;
  height?: number;
}

/**
 * Upload file to Cloudinary
 */
export const uploadToCloudinary = (
  fileBuffer: Buffer,
  options: UploadOptions
): Promise<UploadResult> => {
  return new Promise((resolve, reject) => {
    const uploadStream = cloudinary.uploader.upload_stream(
      {
        folder: options.folder,
        public_id: options.public_id,
        transformation: options.transformation,
        resource_type: options.resource_type || 'auto',
        use_filename: true,
        unique_filename: true,
      },
      (error, result) => {
        if (error) {
          reject(error);
        } else if (result) {
          resolve({
            public_id: result.public_id,
            secure_url: result.secure_url,
            url: result.url,
            format: result.format,
            resource_type: result.resource_type,
            bytes: result.bytes,
            width: result.width,
            height: result.height,
          });
        } else {
          reject(new Error('Upload failed - no result'));
        }
      }
    );

    // Convert buffer to stream and pipe to Cloudinary
    const bufferStream = new Readable();
    bufferStream.push(fileBuffer);
    bufferStream.push(null);
    bufferStream.pipe(uploadStream);
  });
};

/**
 * Delete file from Cloudinary
 */
export const deleteFromCloudinary = async (publicId: string): Promise<void> => {
  try {
    await cloudinary.uploader.destroy(publicId);
  } catch (error) {
    console.error('Error deleting file from Cloudinary:', error);
    throw error;
  }
};

/**
 * Upload profile picture with optimizations
 */
export const uploadProfilePicture = async (
  fileBuffer: Buffer,
  userId: string
): Promise<UploadResult> => {
  const options: UploadOptions = {
    folder: 'alumni-portal/profile-pictures',
    public_id: `profile_${userId}_${Date.now()}`,
    transformation: [
      { width: 400, height: 400, crop: 'fill', gravity: 'face' },
      { quality: 'auto', fetch_format: 'auto' }
    ],
    resource_type: 'image'
  };

  return uploadToCloudinary(fileBuffer, options);
};

/**
 * Upload resume document
 */
export const uploadResume = async (
  fileBuffer: Buffer,
  userId: string,
  originalName: string
): Promise<UploadResult> => {
  const options: UploadOptions = {
    folder: 'alumni-portal/resumes',
    public_id: `resume_${userId}_${Date.now()}`,
    resource_type: 'raw' // For PDF and document files
  };

  return uploadToCloudinary(fileBuffer, options);
};

/**
 * Upload event image with optimizations
 */
export const uploadEventImage = async (
  fileBuffer: Buffer,
  eventId: string
): Promise<UploadResult> => {
  const options: UploadOptions = {
    folder: 'alumni-portal/event-images',
    public_id: `event_${eventId}_${Date.now()}`,
    transformation: [
      { width: 800, height: 600, crop: 'fill' },
      { quality: 'auto', fetch_format: 'auto' }
    ],
    resource_type: 'image'
  };

  return uploadToCloudinary(fileBuffer, options);
};

/**
 * Upload post image with optimizations
 */
export const uploadPostImage = async (
  fileBuffer: Buffer,
  postId: string
): Promise<UploadResult> => {
  const options: UploadOptions = {
    folder: 'alumni-portal/post-images',
    public_id: `post_${postId}_${Date.now()}`,
    transformation: [
      { width: 1200, height: 800, crop: 'limit' },
      { quality: 'auto', fetch_format: 'auto' }
    ],
    resource_type: 'image'
  };

  return uploadToCloudinary(fileBuffer, options);
};

/**
 * Get optimized image URL with transformations
 */
export const getOptimizedImageUrl = (
  publicId: string,
  transformations?: any
): string => {
  if (!transformations) {
    transformations = [
      { quality: 'auto', fetch_format: 'auto' }
    ];
  }

  return cloudinary.url(publicId, {
    transformation: transformations,
    secure: true
  });
};

/**
 * Generate thumbnail URL
 */
export const getThumbnailUrl = (publicId: string, size: number = 150): string => {
  return cloudinary.url(publicId, {
    transformation: [
      { width: size, height: size, crop: 'fill' },
      { quality: 'auto', fetch_format: 'auto' }
    ],
    secure: true
  });
};

/**
 * Validate Cloudinary configuration
 */
export const validateCloudinaryConfig = (): boolean => {
  const { cloud_name, api_key, api_secret } = cloudinary.config();
  
  if (!cloud_name || !api_key || !api_secret) {
    console.error('❌ Cloudinary configuration missing. Please set CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, and CLOUDINARY_API_SECRET environment variables.');
    return false;
  }
  
  console.log('✅ Cloudinary configuration validated');
  return true;
};

/**
 * Get file info from Cloudinary
 */
export const getFileInfo = async (publicId: string) => {
  try {
    const result = await cloudinary.api.resource(publicId);
    return {
      public_id: result.public_id,
      format: result.format,
      resource_type: result.resource_type,
      bytes: result.bytes,
      width: result.width,
      height: result.height,
      created_at: result.created_at,
      secure_url: result.secure_url
    };
  } catch (error) {
    console.error('Error getting file info from Cloudinary:', error);
    throw error;
  }
};

/**
 * List files in a folder
 */
export const listFiles = async (folder: string, maxResults: number = 100) => {
  try {
    const result = await cloudinary.api.resources({
      type: 'upload',
      prefix: folder,
      max_results: maxResults
    });
    
    return result.resources;
  } catch (error) {
    console.error('Error listing files from Cloudinary:', error);
    throw error;
  }
};
