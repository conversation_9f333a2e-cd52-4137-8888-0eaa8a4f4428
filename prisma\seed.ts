import { PrismaClient, UserRole, UserStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      name: 'System Administrator',
      usn: 'ADMIN001',
      course: 'Computer Science',
      batch: '2020',
      role: UserRole.ADMIN,
      status: UserStatus.APPROVED,
      bio: 'System administrator for the Alumni Portal',
      showEmail: true,
    },
  });

  console.log('✅ Admin user created:', admin.email);

  // Create sample alumni users
  const alumniUsers = [
    {
      email: '<EMAIL>',
      name: '<PERSON>',
      usn: 'CS001',
      course: 'Computer Science',
      batch: '2018',
      company: 'Google',
      jobTitle: 'Software Engineer',
      experience: 5,
      location: 'Bangalore, India',
      bio: 'Passionate software engineer with expertise in full-stack development.',
      linkedinUrl: 'https://linkedin.com/in/johndoe',
    },
    {
      email: '<EMAIL>',
      name: 'Jane Smith',
      usn: 'EC002',
      course: 'Electronics and Communication',
      batch: '2017',
      company: 'Microsoft',
      jobTitle: 'Senior Software Engineer',
      experience: 6,
      location: 'Hyderabad, India',
      bio: 'Tech lead with experience in cloud technologies and system design.',
      linkedinUrl: 'https://linkedin.com/in/janesmith',
    },
    {
      email: '<EMAIL>',
      name: 'Alex Johnson',
      usn: 'ME003',
      course: 'Mechanical Engineering',
      batch: '2019',
      company: 'Tesla',
      jobTitle: 'Design Engineer',
      experience: 4,
      location: 'California, USA',
      bio: 'Mechanical engineer working on sustainable transportation solutions.',
      linkedinUrl: 'https://linkedin.com/in/alexjohnson',
    },
  ];

  for (const userData of alumniUsers) {
    const password = await bcrypt.hash('password123', 12);
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        ...userData,
        password,
        role: UserRole.ALUMNI,
        status: UserStatus.APPROVED,
        showLinkedin: true,
      },
    });
    console.log('✅ Alumni user created:', user.email);
  }

  // Create sample student users
  const studentUsers = [
    {
      email: '<EMAIL>',
      name: 'Rahul Kumar',
      usn: 'CS2021001',
      course: 'Computer Science',
      batch: '2025',
      bio: 'Final year CS student interested in web development and AI.',
    },
    {
      email: '<EMAIL>',
      name: 'Priya Sharma',
      usn: 'EC2022002',
      course: 'Electronics and Communication',
      batch: '2026',
      bio: 'Third year ECE student passionate about IoT and embedded systems.',
    },
  ];

  for (const userData of studentUsers) {
    const password = await bcrypt.hash('student123', 12);
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        ...userData,
        password,
        role: UserRole.STUDENT,
        status: UserStatus.APPROVED,
      },
    });
    console.log('✅ Student user created:', user.email);
  }

  // Create sample jobs
  const alumni = await prisma.user.findMany({
    where: { role: UserRole.ALUMNI },
    take: 2,
  });

  if (alumni.length > 0) {
    const sampleJobs = [
      {
        title: 'Frontend Developer Intern',
        company: 'TechCorp',
        location: 'Bangalore, India',
        type: 'INTERNSHIP' as const,
        description: 'Looking for a passionate frontend developer intern to join our team.',
        requirements: 'React, JavaScript, HTML, CSS knowledge required.',
        salary: '₹25,000/month',
        relevantCourses: ['Computer Science', 'Information Technology'],
        postedById: alumni[0].id,
      },
      {
        title: 'Software Engineer',
        company: 'StartupXYZ',
        location: 'Remote',
        type: 'FULL_TIME' as const,
        description: 'Full-stack developer position for building scalable web applications.',
        requirements: 'Node.js, React, PostgreSQL, 2+ years experience',
        salary: '₹8-12 LPA',
        relevantCourses: ['Computer Science', 'Software Engineering'],
        postedById: alumni[1].id,
      },
    ];

    for (const jobData of sampleJobs) {
      const job = await prisma.job.create({
        data: jobData,
      });
      console.log('✅ Job created:', job.title);
    }
  }

  // Create sample events
  if (alumni.length > 0) {
    const sampleEvents = [
      {
        title: 'Tech Talk: Future of AI',
        description: 'Join us for an insightful discussion about the future of artificial intelligence.',
        location: 'College Auditorium',
        startTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000), // 2 hours duration
        organizerId: alumni[0].id,
      },
      {
        title: 'Alumni Networking Meetup',
        description: 'Connect with fellow alumni and expand your professional network.',
        location: 'City Hotel, Conference Hall',
        startTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
        endTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000), // 3 hours duration
        organizerId: alumni[1].id,
      },
    ];

    for (const eventData of sampleEvents) {
      const event = await prisma.event.create({
        data: eventData,
      });
      console.log('✅ Event created:', event.title);
    }
  }

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
