import { Request, Response, NextFunction } from 'express';
import { prisma } from '@/config/database';
import { createError } from '@/middleware/errorHandler';
import { validateFileSize } from '@/middleware/upload';
import * as cloudinaryService from '@/services/cloudinaryService';

/**
 * Upload profile picture
 */
export const uploadProfilePicture = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    if (!req.file) {
      throw createError('No file uploaded', 400);
    }

    // Validate file size
    if (!validateFileSize(req.file)) {
      throw createError('File size exceeds limit', 400);
    }

    // Upload to Cloudinary
    const uploadResult = await cloudinaryService.uploadProfilePicture(
      req.file.buffer,
      req.user.userId
    );

    // Update user profile with new profile picture URL
    const updatedUser = await prisma.user.update({
      where: { id: req.user.userId },
      data: { profilePicture: uploadResult.secure_url },
      select: {
        id: true,
        name: true,
        profilePicture: true,
      }
    });

    res.json({
      message: 'Profile picture uploaded successfully',
      user: updatedUser,
      file: {
        url: uploadResult.secure_url,
        publicId: uploadResult.public_id,
        format: uploadResult.format,
        size: uploadResult.bytes,
        width: uploadResult.width,
        height: uploadResult.height,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Upload resume
 */
export const uploadResume = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    if (!req.file) {
      throw createError('No file uploaded', 400);
    }

    // Validate file size
    if (!validateFileSize(req.file)) {
      throw createError('File size exceeds limit', 400);
    }

    // Upload to Cloudinary
    const uploadResult = await cloudinaryService.uploadResume(
      req.file.buffer,
      req.user.userId,
      req.file.originalname
    );

    // Update user profile with new resume URL
    const updatedUser = await prisma.user.update({
      where: { id: req.user.userId },
      data: { resumeUrl: uploadResult.secure_url },
      select: {
        id: true,
        name: true,
        resumeUrl: true,
      }
    });

    res.json({
      message: 'Resume uploaded successfully',
      user: updatedUser,
      file: {
        url: uploadResult.secure_url,
        publicId: uploadResult.public_id,
        format: uploadResult.format,
        size: uploadResult.bytes,
        originalName: req.file.originalname,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Upload event image
 */
export const uploadEventImage = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    if (!req.file) {
      throw createError('No file uploaded', 400);
    }

    const { eventId } = req.body;

    if (!eventId) {
      throw createError('Event ID is required', 400);
    }

    // Verify user owns the event or is admin
    const event = await prisma.event.findUnique({
      where: { id: eventId },
      select: {
        id: true,
        organizerId: true,
      }
    });

    if (!event) {
      throw createError('Event not found', 404);
    }

    if (event.organizerId !== req.user.userId && req.user.role !== 'ADMIN') {
      throw createError('Not authorized to upload image for this event', 403);
    }

    // Validate file size
    if (!validateFileSize(req.file)) {
      throw createError('File size exceeds limit', 400);
    }

    // Upload to Cloudinary
    const uploadResult = await cloudinaryService.uploadEventImage(
      req.file.buffer,
      eventId
    );

    // Update event with new image URL
    const updatedEvent = await prisma.event.update({
      where: { id: eventId },
      data: { imageUrl: uploadResult.secure_url },
      select: {
        id: true,
        title: true,
        imageUrl: true,
      }
    });

    res.json({
      message: 'Event image uploaded successfully',
      event: updatedEvent,
      file: {
        url: uploadResult.secure_url,
        publicId: uploadResult.public_id,
        format: uploadResult.format,
        size: uploadResult.bytes,
        width: uploadResult.width,
        height: uploadResult.height,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Upload post image
 */
export const uploadPostImage = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    if (!req.file) {
      throw createError('No file uploaded', 400);
    }

    const { postId } = req.body;

    if (!postId) {
      throw createError('Post ID is required', 400);
    }

    // Verify user owns the post or is admin
    const post = await prisma.post.findUnique({
      where: { id: postId },
      select: {
        id: true,
        authorId: true,
      }
    });

    if (!post) {
      throw createError('Post not found', 404);
    }

    if (post.authorId !== req.user.userId && req.user.role !== 'ADMIN') {
      throw createError('Not authorized to upload image for this post', 403);
    }

    // Validate file size
    if (!validateFileSize(req.file)) {
      throw createError('File size exceeds limit', 400);
    }

    // Upload to Cloudinary
    const uploadResult = await cloudinaryService.uploadPostImage(
      req.file.buffer,
      postId
    );

    // Update post with new image URL
    const updatedPost = await prisma.post.update({
      where: { id: postId },
      data: { imageUrl: uploadResult.secure_url },
      select: {
        id: true,
        title: true,
        imageUrl: true,
      }
    });

    res.json({
      message: 'Post image uploaded successfully',
      post: updatedPost,
      file: {
        url: uploadResult.secure_url,
        publicId: uploadResult.public_id,
        format: uploadResult.format,
        size: uploadResult.bytes,
        width: uploadResult.width,
        height: uploadResult.height,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete file from Cloudinary
 */
export const deleteFile = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const { publicId } = req.params;

    if (!publicId) {
      throw createError('Public ID is required', 400);
    }

    // Delete from Cloudinary
    await cloudinaryService.deleteFromCloudinary(publicId);

    res.json({
      message: 'File deleted successfully',
      publicId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
