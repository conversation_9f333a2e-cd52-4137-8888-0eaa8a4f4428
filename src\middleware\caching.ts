import { Request, Response, NextFunction } from "express";
import { AdvancedCacheService } from "@/services/cacheService";
import { Logger } from "@/services/loggerService";

// Cache configuration interface
interface CacheOptions {
  ttl?: number; // Time to live in seconds
  keyGenerator?: (req: Request) => string; // Custom key generator
  condition?: (req: Request, res: Response) => boolean; // Condition to cache
  skipCache?: (req: Request) => boolean; // Skip cache condition
  varyBy?: string[]; // Headers to vary cache by
}

// Default cache configuration
const DEFAULT_CACHE_OPTIONS: Required<CacheOptions> = {
  ttl: 300, // 5 minutes
  keyGenerator: (req: Request) => `api:${req.method}:${req.originalUrl}`,
  condition: (req: Request, res: Response) => {
    // Only cache GET requests with 200 status
    return req.method === 'GET' && res.statusCode === 200;
  },
  skipCache: (req: Request) => {
    // Skip cache for authenticated requests by default
    return !!req.headers.authorization;
  },
  varyBy: ['accept', 'accept-encoding'],
};

// Response caching middleware
export const cacheResponse = (options: CacheOptions = {}) => {
  const config = { ...DEFAULT_CACHE_OPTIONS, ...options };

  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip caching if condition not met
    if (config.skipCache(req)) {
      return next();
    }

    // Generate cache key
    let cacheKey = config.keyGenerator(req);
    
    // Add vary headers to cache key
    if (config.varyBy.length > 0) {
      const varyValues = config.varyBy
        .map(header => `${header}:${req.get(header) || 'none'}`)
        .join('|');
      cacheKey += `|${varyValues}`;
    }

    try {
      // Try to get cached response
      const cachedResponse = await AdvancedCacheService.get<{
        statusCode: number;
        headers: Record<string, string>;
        body: any;
      }>(cacheKey);

      if (cachedResponse) {
        Logger.cache(`Cache hit for ${req.method} ${req.originalUrl}`, cacheKey);
        
        // Set cached headers
        Object.entries(cachedResponse.headers).forEach(([key, value]) => {
          res.set(key, value);
        });
        
        // Add cache headers
        res.set('X-Cache', 'HIT');
        res.set('X-Cache-Key', cacheKey);
        
        return res.status(cachedResponse.statusCode).json(cachedResponse.body);
      }

      // Cache miss - continue with request
      Logger.cache(`Cache miss for ${req.method} ${req.originalUrl}`, cacheKey);
      
      // Override res.json to cache the response
      const originalJson = res.json.bind(res);
      const originalStatus = res.status.bind(res);
      let statusCode = 200;

      res.status = (code: number) => {
        statusCode = code;
        return originalStatus(code);
      };

      res.json = (body: any) => {
        // Check if we should cache this response
        if (config.condition(req, res)) {
          // Prepare response data for caching
          const responseData = {
            statusCode,
            headers: {
              'content-type': 'application/json',
              ...Object.fromEntries(
                Object.entries(res.getHeaders()).map(([key, value]) => [
                  key,
                  String(value)
                ])
              ),
            },
            body,
          };

          // Cache the response asynchronously
          AdvancedCacheService.set(cacheKey, responseData, config.ttl)
            .then(() => {
              Logger.cache(`Response cached for ${req.method} ${req.originalUrl}`, cacheKey, {
                ttl: config.ttl,
                statusCode,
              });
            })
            .catch((error) => {
              Logger.error("Failed to cache response", { cacheKey, error });
            });
        }

        // Add cache headers
        res.set('X-Cache', 'MISS');
        res.set('X-Cache-Key', cacheKey);
        
        return originalJson(body);
      };

      next();
    } catch (error) {
      Logger.error("Cache middleware error", { cacheKey, error });
      next();
    }
  };
};

// Cache invalidation middleware
export const invalidateCache = (patterns: string[] | ((req: Request) => string[])) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Store original end function
    const originalEnd = res.end.bind(res);

    res.end = function(chunk?: any, encoding?: any) {
      // Only invalidate on successful operations
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const invalidationPatterns = typeof patterns === 'function' ? patterns(req) : patterns;
        
        // Invalidate cache patterns asynchronously
        Promise.all(
          invalidationPatterns.map(pattern => AdvancedCacheService.invalidatePattern(pattern))
        ).then((results) => {
          const totalInvalidated = results.reduce((sum, count) => sum + count, 0);
          Logger.cache(`Cache invalidated for patterns: ${invalidationPatterns.join(', ')}`, '', {
            patterns: invalidationPatterns,
            totalInvalidated,
          });
        }).catch((error) => {
          Logger.error("Cache invalidation error", { patterns: invalidationPatterns, error });
        });
      }

      return originalEnd(chunk, encoding);
    };

    next();
  };
};

// Cache warming utility
export const warmApiCache = async (routes: Array<{
  method: string;
  path: string;
  headers?: Record<string, string>;
  ttl?: number;
}>) => {
  Logger.info(`Starting API cache warming for ${routes.length} routes`);
  
  const startTime = Date.now();
  const results = await Promise.allSettled(
    routes.map(async ({ method, path, headers = {}, ttl = 300 }) => {
      try {
        // This would typically make an internal request to warm the cache
        // For now, we'll just log the warming attempt
        const cacheKey = `api:${method}:${path}`;
        Logger.cache(`Cache warming for ${method} ${path}`, cacheKey);
        return { route: `${method} ${path}`, success: true };
      } catch (error) {
        Logger.error(`Cache warming failed for ${method} ${path}`, error);
        return { route: `${method} ${path}`, success: false, error };
      }
    })
  );

  const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
  const duration = Date.now() - startTime;
  
  Logger.performance("API cache warming completed", duration, {
    total: routes.length,
    successful,
    failed: routes.length - successful,
  });
};

// Cache statistics middleware
export const cacheStats = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (req.path === '/cache/stats' && req.method === 'GET') {
      const stats = AdvancedCacheService.getStats();
      return res.json({
        cache: stats,
        timestamp: new Date().toISOString(),
      });
    }
    next();
  };
};

// Cache management middleware
export const cacheManagement = () => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (req.path.startsWith('/cache/')) {
      // Only allow cache management for admin users
      // This should be protected by authentication middleware
      
      switch (req.path) {
        case '/cache/clear':
          if (req.method === 'POST') {
            await AdvancedCacheService.clearAll();
            return res.json({ message: 'Cache cleared successfully' });
          }
          break;
          
        case '/cache/stats/reset':
          if (req.method === 'POST') {
            AdvancedCacheService.resetStats();
            return res.json({ message: 'Cache statistics reset' });
          }
          break;
          
        case '/cache/invalidate':
          if (req.method === 'POST') {
            const { pattern } = req.body;
            if (pattern) {
              const invalidated = await AdvancedCacheService.invalidatePattern(pattern);
              return res.json({ 
                message: `Invalidated ${invalidated} cache entries`,
                pattern,
                invalidated 
              });
            }
            return res.status(400).json({ error: 'Pattern is required' });
          }
          break;
      }
    }
    next();
  };
};

// Predefined cache configurations for common use cases
export const CacheConfigs = {
  // Short-term cache for frequently changing data
  shortTerm: {
    ttl: 60, // 1 minute
  },
  
  // Medium-term cache for moderately changing data
  mediumTerm: {
    ttl: 300, // 5 minutes
  },
  
  // Long-term cache for rarely changing data
  longTerm: {
    ttl: 3600, // 1 hour
  },
  
  // User-specific cache (includes user ID in key)
  userSpecific: {
    ttl: 300,
    keyGenerator: (req: Request) => {
      const userId = req.user?.userId || 'anonymous';
      return `api:user:${userId}:${req.method}:${req.originalUrl}`;
    },
    skipCache: () => false, // Cache even authenticated requests
  },
  
  // Public data cache (longer TTL, no user-specific data)
  publicData: {
    ttl: 1800, // 30 minutes
    skipCache: () => false,
  },
};
