import request from 'supertest';
import { app } from '../src/app';
import { prisma } from '../src/config/database';
import { AuthUtils } from '../src/utils/auth';

describe('Authentication Endpoints', () => {
  describe('POST /api/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123!',
        name: 'Test User',
        mobile: '+1234567890',
        usn: '1AB12CS001',
        course: 'Computer Science',
        batch: '2020',
        role: 'STUDENT'
      };

      // Mock Prisma calls
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);
      (prisma.user.create as jest.Mock).mockResolvedValue({
        id: 'user-id',
        email: userData.email,
        name: userData.name,
        role: userData.role,
        status: 'PENDING'
      });

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.message).toBe('Registration successful');
      expect(response.body.user.email).toBe(userData.email);
      expect(prisma.user.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          email: userData.email,
          name: userData.name,
          role: userData.role,
          status: 'PENDING'
        })
      });
    });

    it('should return 409 if user already exists', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123!',
        name: 'Existing User',
        usn: '1AB12CS002',
        course: 'Computer Science',
        batch: '2020',
        role: 'STUDENT'
      };

      // Mock existing user
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'existing-user-id',
        email: userData.email
      });

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(409);

      expect(response.body.error).toBe('User already exists');
    });

    it('should return 400 for invalid input', async () => {
      const invalidData = {
        email: 'invalid-email',
        password: '123', // Too short
        name: '', // Empty name
        role: 'INVALID_ROLE'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('Validation failed');
      expect(response.body.details).toBeInstanceOf(Array);
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!'
      };

      const mockUser = {
        id: 'user-id',
        email: loginData.email,
        password: await AuthUtils.hashPassword(loginData.password),
        name: 'Test User',
        role: 'STUDENT',
        status: 'APPROVED'
      };

      // Mock Prisma calls
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
      (prisma.user.update as jest.Mock).mockResolvedValue(mockUser);

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.message).toBe('Login successful');
      expect(response.body.user.email).toBe(loginData.email);
      expect(response.body.tokens.accessToken).toBeDefined();
      expect(response.body.tokens.refreshToken).toBeDefined();
    });

    it('should return 401 for invalid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword'
      };

      // Mock user not found
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.error).toBe('Invalid credentials');
    });

    it('should return 403 for pending user', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!'
      };

      const mockUser = {
        id: 'user-id',
        email: loginData.email,
        password: await AuthUtils.hashPassword(loginData.password),
        status: 'PENDING'
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(403);

      expect(response.body.error).toBe('Account pending approval');
    });
  });

  describe('POST /api/auth/refresh', () => {
    it('should refresh tokens successfully', async () => {
      const mockUser = {
        id: 'user-id',
        email: '<EMAIL>',
        role: 'STUDENT',
        status: 'APPROVED'
      };

      const refreshToken = AuthUtils.generateRefreshToken({
        userId: mockUser.id,
        email: mockUser.email,
        role: mockUser.role
      });

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body.message).toBe('Token refreshed successfully');
      expect(response.body.tokens.accessToken).toBeDefined();
      expect(response.body.tokens.refreshToken).toBeDefined();
    });

    it('should return 401 for invalid refresh token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: 'invalid-token' })
        .expect(401);

      expect(response.body.error).toBe('Invalid refresh token');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .expect(200);

      expect(response.body.message).toBe('Logout successful');
    });
  });
});

describe('AuthUtils', () => {
  describe('validatePassword', () => {
    it('should validate strong password', () => {
      const result = AuthUtils.validatePassword('StrongPass123!');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject weak password', () => {
      const result = AuthUtils.validatePassword('weak');
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('validateUSN', () => {
    it('should validate correct USN format', () => {
      expect(AuthUtils.validateUSN('1AB12CS001')).toBe(true);
      expect(AuthUtils.validateUSN('4AL15CS123')).toBe(true);
    });

    it('should reject invalid USN format', () => {
      expect(AuthUtils.validateUSN('invalid')).toBe(false);
      expect(AuthUtils.validateUSN('123')).toBe(false);
    });
  });

  describe('hashPassword and comparePassword', () => {
    it('should hash and compare password correctly', async () => {
      const password = 'TestPassword123!';
      const hashedPassword = await AuthUtils.hashPassword(password);
      
      expect(hashedPassword).not.toBe(password);
      
      const isMatch = await AuthUtils.comparePassword(password, hashedPassword);
      expect(isMatch).toBe(true);
      
      const isWrongMatch = await AuthUtils.comparePassword('WrongPassword', hashedPassword);
      expect(isWrongMatch).toBe(false);
    });
  });
});
