# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/alumni_portal?schema=public&connection_limit=20&pool_timeout=20&socket_timeout=60"
DATABASE_READ_REPLICA_URL="postgresql://username:password@localhost:5432/alumni_portal_read?schema=public&connection_limit=15&pool_timeout=20&socket_timeout=60"

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-token-secret-here
JWT_REFRESH_EXPIRES_IN=30d

# Bcrypt Configuration
BCRYPT_SALT_ROUNDS=12

# Cloudinary Configuration (for file uploads)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Alumni Portal"

# WebSocket Configuration
WS_PORT=3001

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# CORS Configuration
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Clustering Configuration
CLUSTER_ENABLED=true
CLUSTER_WORKERS=4
CLUSTER_RESTART_DELAY=1000
CLUSTER_MAX_RESTARTS=5
CLUSTER_SHUTDOWN_TIMEOUT=10000

# Redis Configuration (for caching and queues)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Memory Cache Configuration
MEMORY_CACHE_MAX_SIZE=1000
MEMORY_CACHE_TTL=300
MEMORY_CACHE_CLEANUP_INTERVAL=60000
