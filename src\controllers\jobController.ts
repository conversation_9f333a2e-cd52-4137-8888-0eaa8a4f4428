import { Request, Response, NextFunction } from "express";
import { JobType } from "@prisma/client";
import { prisma } from "@/config/database";
import { createError } from "@/middleware/errorHandler";

interface CreateJobRequest {
  title: string;
  company: string;
  location: string;
  type: JobType;
  description: string;
  requirements?: string;
  salary?: string;
  applicationUrl?: string;
  allowResume: boolean;
  relevantCourses: string[];
  expiresAt?: string;
}

interface ApplyToJobRequest {
  resumeUrl?: string;
  message?: string;
}

interface UpdateApplicationStatusRequest {
  status: "PENDING" | "REVIEWED" | "ACCEPTED" | "REJECTED";
}

/**
 * Get all jobs with search and filtering
 */
export const getJobs = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const search = req.query.search as string;
    const type = req.query.type as JobType;
    const location = req.query.location as string;
    const company = req.query.company as string;
    const course = req.query.course as string;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      isActive: true,
      OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }],
    };

    if (search) {
      where.AND = [
        {
          OR: [
            { title: { contains: search, mode: "insensitive" } },
            { company: { contains: search, mode: "insensitive" } },
            { description: { contains: search, mode: "insensitive" } },
            { requirements: { contains: search, mode: "insensitive" } },
          ],
        },
      ];
    }

    if (type) {
      where.type = type;
    }

    if (location) {
      where.location = { contains: location, mode: "insensitive" };
    }

    if (company) {
      where.company = { contains: company, mode: "insensitive" };
    }

    if (course) {
      where.relevantCourses = {
        has: course,
      };
    }

    const [jobs, total] = await Promise.all([
      prisma.job.findMany({
        where,
        include: {
          postedBy: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              company: true,
              jobTitle: true,
            },
          },
          _count: {
            select: {
              applications: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.job.count({ where }),
    ]);

    res.json({
      jobs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get job by ID
 */
export const getJobById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const job = await prisma.job.findUnique({
      where: {
        id,
        isActive: true,
      },
      include: {
        postedBy: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
            linkedinUrl: true,
            showLinkedin: true,
          },
        },
        _count: {
          select: {
            applications: true,
          },
        },
      },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    // Check if current user has applied (if authenticated)
    let hasApplied = false;
    if (req.user) {
      const application = await prisma.jobApplication.findUnique({
        where: {
          jobId_applicantId: {
            jobId: id,
            applicantId: req.user.userId,
          },
        },
      });
      hasApplied = !!application;
    }

    // Filter poster's contact info based on privacy settings
    const filteredJob = {
      ...job,
      postedBy: {
        ...job.postedBy,
        linkedinUrl: job.postedBy.showLinkedin ? job.postedBy.linkedinUrl : null,
      },
      hasApplied,
    };

    res.json({
      job: filteredJob,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new job (Alumni only)
 */
export const createJob = async (req: Request<{}, {}, CreateJobRequest>, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const jobData = req.body;

    // Parse expiresAt if provided
    const expiresAt = jobData.expiresAt ? new Date(jobData.expiresAt) : null;

    const job = await prisma.job.create({
      data: {
        ...jobData,
        expiresAt,
        postedById: req.user.userId,
      },
      include: {
        postedBy: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
          },
        },
      },
    });

    // Create notifications for relevant students
    // TODO: Implement notification system for job postings

    res.status(201).json({
      message: "Job posted successfully",
      job,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update job (Alumni only - own jobs)
 */
export const updateJob = async (
  req: Request<{ id: string }, {}, CreateJobRequest>,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const updateData = req.body;

    // Check if job exists and belongs to current user
    const existingJob = await prisma.job.findUnique({
      where: { id },
      select: {
        id: true,
        postedById: true,
      },
    });

    if (!existingJob) {
      throw createError("Job not found", 404);
    }

    if (existingJob.postedById !== req.user.userId) {
      throw createError("Not authorized to update this job", 403);
    }

    // Parse expiresAt if provided
    const expiresAt = updateData.expiresAt ? new Date(updateData.expiresAt) : null;

    const updatedJob = await prisma.job.update({
      where: { id },
      data: {
        ...updateData,
        expiresAt,
      },
      include: {
        postedBy: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
          },
        },
      },
    });

    res.json({
      message: "Job updated successfully",
      job: updatedJob,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get jobs posted by current user (Alumni only)
 */
export const getMyPostedJobs = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const [jobs, total] = await Promise.all([
      prisma.job.findMany({
        where: {
          postedById: req.user.userId,
          isActive: true,
        },
        include: {
          _count: {
            select: {
              applications: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.job.count({
        where: {
          postedById: req.user.userId,
          isActive: true,
        },
      }),
    ]);

    res.json({
      jobs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Apply to a job
 */
export const applyToJob = async (
  req: Request<{ id: string }, {}, ApplyToJobRequest>,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id: jobId } = req.params;
    const { resumeUrl, message } = req.body;

    // Check if job exists and is active
    const job = await prisma.job.findUnique({
      where: {
        id: jobId,
        isActive: true,
      },
      select: {
        id: true,
        title: true,
        company: true,
        allowResume: true,
        expiresAt: true,
        postedById: true,
        postedBy: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!job) {
      throw createError("Job not found or no longer active", 404);
    }

    // Check if job has expired
    if (job.expiresAt && job.expiresAt < new Date()) {
      throw createError("This job posting has expired", 410);
    }

    // Check if user is trying to apply to their own job
    if (job.postedById === req.user.userId) {
      throw createError("Cannot apply to your own job posting", 400);
    }

    // Check if user has already applied
    const existingApplication = await prisma.jobApplication.findUnique({
      where: {
        jobId_applicantId: {
          jobId,
          applicantId: req.user.userId,
        },
      },
    });

    if (existingApplication) {
      throw createError("You have already applied to this job", 409);
    }

    // Validate resume requirement
    if (job.allowResume && !resumeUrl) {
      throw createError("Resume is required for this job application", 400);
    }

    // Create application
    const application = await prisma.jobApplication.create({
      data: {
        jobId,
        applicantId: req.user.userId,
        resumeUrl,
        message,
      },
      include: {
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            course: true,
            batch: true,
            profilePicture: true,
          },
        },
        job: {
          select: {
            id: true,
            title: true,
            company: true,
          },
        },
      },
    });

    // Create notification for job poster
    await prisma.notification.create({
      data: {
        userId: job.postedById,
        type: "JOB_POSTED",
        title: "New Job Application",
        message: `${req.user.email} applied to your job: ${job.title}`,
        data: {
          jobId,
          applicationId: application.id,
          applicantId: req.user.userId,
        },
      },
    });

    res.status(201).json({
      message: "Application submitted successfully",
      application: {
        id: application.id,
        job: application.job,
        message: application.message,
        status: application.status,
        createdAt: application.createdAt,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get applications for a specific job (Alumni only - own jobs)
 */
export const getJobApplications = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id: jobId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    // Check if job exists and belongs to current user
    const job = await prisma.job.findUnique({
      where: { id: jobId },
      select: {
        id: true,
        title: true,
        postedById: true,
      },
    });

    if (!job) {
      throw createError("Job not found", 404);
    }

    if (job.postedById !== req.user.userId) {
      throw createError("Not authorized to view applications for this job", 403);
    }

    const [applications, total] = await Promise.all([
      prisma.jobApplication.findMany({
        where: { jobId },
        include: {
          applicant: {
            select: {
              id: true,
              name: true,
              email: true,
              mobile: true,
              course: true,
              batch: true,
              profilePicture: true,
              bio: true,
              linkedinUrl: true,
              githubUrl: true,
              portfolioUrl: true,
              showEmail: true,
              showMobile: true,
              showLinkedin: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.jobApplication.count({ where: { jobId } }),
    ]);

    // Filter applicant contact info based on privacy settings
    const filteredApplications = applications.map((app) => ({
      ...app,
      applicant: {
        ...app.applicant,
        email: app.applicant.showEmail ? app.applicant.email : null,
        mobile: app.applicant.showMobile ? app.applicant.mobile : null,
        linkedinUrl: app.applicant.showLinkedin ? app.applicant.linkedinUrl : null,
      },
    }));

    res.json({
      job: {
        id: job.id,
        title: job.title,
      },
      applications: filteredApplications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete job (Alumni only - own jobs)
 */
export const deleteJob = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;

    // Check if job exists and belongs to current user
    const existingJob = await prisma.job.findUnique({
      where: { id },
      select: {
        id: true,
        postedById: true,
        title: true,
      },
    });

    if (!existingJob) {
      throw createError("Job not found", 404);
    }

    if (existingJob.postedById !== req.user.userId) {
      throw createError("Not authorized to delete this job", 403);
    }

    // Soft delete by setting isActive to false
    await prisma.job.update({
      where: { id },
      data: { isActive: false },
    });

    res.json({
      message: "Job deleted successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get current user's job applications
 */
export const getMyApplications = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const [applications, total] = await Promise.all([
      prisma.jobApplication.findMany({
        where: { applicantId: req.user.userId },
        include: {
          job: {
            select: {
              id: true,
              title: true,
              company: true,
              location: true,
              type: true,
              isActive: true,
              postedBy: {
                select: {
                  id: true,
                  name: true,
                  profilePicture: true,
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.jobApplication.count({ where: { applicantId: req.user.userId } }),
    ]);

    res.json({
      applications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update application status (Alumni only - for applications to their jobs)
 */
export const updateApplicationStatus = async (
  req: Request<{ id: string }, {}, UpdateApplicationStatusRequest>,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id: applicationId } = req.params;
    const { status } = req.body;

    // Find application and verify ownership
    const application = await prisma.jobApplication.findUnique({
      where: { id: applicationId },
      include: {
        job: {
          select: {
            id: true,
            title: true,
            postedById: true,
          },
        },
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!application) {
      throw createError("Application not found", 404);
    }

    if (application.job.postedById !== req.user.userId) {
      throw createError("Not authorized to update this application", 403);
    }

    // Update application status
    const updatedApplication = await prisma.jobApplication.update({
      where: { id: applicationId },
      data: { status },
      include: {
        job: {
          select: {
            id: true,
            title: true,
            company: true,
          },
        },
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Create notification for applicant
    let notificationMessage = "";
    switch (status) {
      case "REVIEWED":
        notificationMessage = `Your application for ${application.job.title} is being reviewed`;
        break;
      case "ACCEPTED":
        notificationMessage = `Congratulations! Your application for ${application.job.title} has been accepted`;
        break;
      case "REJECTED":
        notificationMessage = `Your application for ${application.job.title} was not selected`;
        break;
    }

    if (notificationMessage) {
      await prisma.notification.create({
        data: {
          userId: application.applicant.id,
          type: "JOB_POSTED",
          title: "Application Status Update",
          message: notificationMessage,
          data: {
            jobId: application.job.id,
            applicationId,
            status,
          },
        },
      });
    }

    res.json({
      message: "Application status updated successfully",
      application: {
        id: updatedApplication.id,
        status: updatedApplication.status,
        job: updatedApplication.job,
        applicant: updatedApplication.applicant,
        updatedAt: updatedApplication.updatedAt,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
