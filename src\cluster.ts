import cluster from "cluster";
import os from "os";
import { Logger } from "@/services/loggerService";

// Cluster configuration
const CLUSTER_CONFIG = {
  // Number of workers (default to CPU count, but allow override)
  workers: parseInt(process.env.CLUSTER_WORKERS || os.cpus().length.toString()),
  // Enable clustering (can be disabled for development)
  enabled: process.env.CLUSTER_ENABLED !== "false",
  // Restart delay for failed workers
  restartDelay: parseInt(process.env.CLUSTER_RESTART_DELAY || "1000"),
  // Maximum restart attempts
  maxRestarts: parseInt(process.env.CLUSTER_MAX_RESTARTS || "5"),
  // Grace period for worker shutdown
  shutdownTimeout: parseInt(process.env.CLUSTER_SHUTDOWN_TIMEOUT || "10000"),
};

// Worker restart tracking
const workerRestarts = new Map<number, number>();

export class ClusterManager {
  private static shutdownInProgress = false;

  /**
   * Initialize cluster if enabled and this is the master process
   */
  static init(): boolean {
    // Skip clustering in development or if disabled
    if (process.env.NODE_ENV === "development" || !CLUSTER_CONFIG.enabled) {
      Logger.startup("Clustering disabled, running in single process mode");
      return false;
    }

    if (cluster.isPrimary) {
      this.setupMaster();
      return true;
    }

    return false;
  }

  /**
   * Setup master process
   */
  private static setupMaster(): void {
    Logger.startup(`Master process ${process.pid} starting with ${CLUSTER_CONFIG.workers} workers`);

    // Set up cluster settings
    cluster.setupPrimary({
      exec: __filename,
      silent: false,
    });

    // Fork workers
    for (let i = 0; i < CLUSTER_CONFIG.workers; i++) {
      this.forkWorker();
    }

    // Handle worker events
    this.setupWorkerEventHandlers();

    // Handle master process signals
    this.setupMasterSignalHandlers();

    // Log cluster status
    this.logClusterStatus();
  }

  /**
   * Fork a new worker
   */
  private static forkWorker(): void {
    const worker = cluster.fork();
    
    Logger.info(`Worker ${worker.process.pid} started`);

    // Track worker startup time
    const startTime = Date.now();
    
    worker.on("online", () => {
      const bootTime = Date.now() - startTime;
      Logger.performance(`Worker ${worker.process.pid} online`, bootTime);
    });

    worker.on("listening", (address) => {
      Logger.info(`Worker ${worker.process.pid} listening on ${address.address}:${address.port}`);
    });
  }

  /**
   * Setup worker event handlers
   */
  private static setupWorkerEventHandlers(): void {
    cluster.on("exit", (worker, code, signal) => {
      if (this.shutdownInProgress) {
        Logger.info(`Worker ${worker.process.pid} exited during shutdown`);
        return;
      }

      Logger.error(`Worker ${worker.process.pid} died`, {
        code,
        signal,
        pid: worker.process.pid,
      });

      // Check restart attempts
      const restartCount = workerRestarts.get(worker.id) || 0;
      
      if (restartCount < CLUSTER_CONFIG.maxRestarts) {
        workerRestarts.set(worker.id, restartCount + 1);
        
        Logger.info(`Restarting worker ${worker.process.pid} (attempt ${restartCount + 1}/${CLUSTER_CONFIG.maxRestarts})`);
        
        // Restart after delay
        setTimeout(() => {
          this.forkWorker();
        }, CLUSTER_CONFIG.restartDelay);
      } else {
        Logger.error(`Worker ${worker.process.pid} exceeded maximum restart attempts`);
      }
    });

    cluster.on("disconnect", (worker) => {
      Logger.warn(`Worker ${worker.process.pid} disconnected`);
    });
  }

  /**
   * Setup master signal handlers for graceful shutdown
   */
  private static setupMasterSignalHandlers(): void {
    const gracefulShutdown = (signal: string) => {
      if (this.shutdownInProgress) {
        Logger.warn(`${signal} received again, forcing exit`);
        process.exit(1);
      }

      this.shutdownInProgress = true;
      Logger.info(`${signal} received, shutting down cluster gracefully`);

      // Disconnect all workers
      const workers = Object.values(cluster.workers || {});
      const shutdownPromises = workers.map((worker) => {
        if (worker) {
          return this.shutdownWorker(worker);
        }
        return Promise.resolve();
      });

      // Wait for all workers to shutdown or timeout
      Promise.allSettled(shutdownPromises)
        .then(() => {
          Logger.info("All workers shut down, exiting master");
          process.exit(0);
        })
        .catch((error) => {
          Logger.error("Error during cluster shutdown", error);
          process.exit(1);
        });

      // Force exit after timeout
      setTimeout(() => {
        Logger.error("Cluster shutdown timeout, forcing exit");
        process.exit(1);
      }, CLUSTER_CONFIG.shutdownTimeout);
    };

    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));
  }

  /**
   * Shutdown a worker gracefully
   */
  private static shutdownWorker(worker: cluster.Worker): Promise<void> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        Logger.warn(`Worker ${worker.process.pid} shutdown timeout, killing`);
        worker.kill("SIGKILL");
        resolve();
      }, CLUSTER_CONFIG.shutdownTimeout / 2);

      worker.on("disconnect", () => {
        clearTimeout(timeout);
        Logger.info(`Worker ${worker.process.pid} disconnected gracefully`);
        resolve();
      });

      worker.on("exit", () => {
        clearTimeout(timeout);
        resolve();
      });

      // Send disconnect signal
      worker.disconnect();
    });
  }

  /**
   * Log cluster status
   */
  private static logClusterStatus(): void {
    setInterval(() => {
      const workers = Object.values(cluster.workers || {});
      const aliveWorkers = workers.filter((worker) => worker && !worker.isDead());
      
      Logger.info(`Cluster status: ${aliveWorkers.length}/${CLUSTER_CONFIG.workers} workers alive`);
      
      // Log memory usage
      const memUsage = process.memoryUsage();
      Logger.performance("Master memory usage", 0, {
        rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
        external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
      });
    }, 30000); // Log every 30 seconds
  }

  /**
   * Get cluster information
   */
  static getClusterInfo() {
    if (!cluster.isPrimary) {
      return {
        isMaster: false,
        workerId: cluster.worker?.id,
        workerPid: process.pid,
      };
    }

    const workers = Object.values(cluster.workers || {});
    return {
      isMaster: true,
      masterPid: process.pid,
      totalWorkers: CLUSTER_CONFIG.workers,
      aliveWorkers: workers.filter((worker) => worker && !worker.isDead()).length,
      workers: workers.map((worker) => ({
        id: worker?.id,
        pid: worker?.process.pid,
        state: worker?.state,
        isDead: worker?.isDead(),
      })),
    };
  }
}

// Worker-specific setup
export const setupWorkerProcess = () => {
  if (cluster.isWorker) {
    Logger.startup(`Worker ${process.pid} (ID: ${cluster.worker?.id}) starting`);

    // Handle worker signals for graceful shutdown
    const gracefulWorkerShutdown = (signal: string) => {
      Logger.info(`Worker ${process.pid} received ${signal}, shutting down gracefully`);
      
      // Perform cleanup here (close database connections, etc.)
      // The actual server shutdown will be handled in the main application
      
      setTimeout(() => {
        Logger.info(`Worker ${process.pid} shutdown complete`);
        process.exit(0);
      }, 1000);
    };

    process.on("SIGTERM", () => gracefulWorkerShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulWorkerShutdown("SIGINT"));
    
    // Handle uncaught exceptions in worker
    process.on("uncaughtException", (error) => {
      Logger.error(`Uncaught exception in worker ${process.pid}`, error);
      process.exit(1);
    });

    process.on("unhandledRejection", (reason, promise) => {
      Logger.error(`Unhandled rejection in worker ${process.pid}`, {
        reason,
        promise,
      });
      process.exit(1);
    });
  }
};
