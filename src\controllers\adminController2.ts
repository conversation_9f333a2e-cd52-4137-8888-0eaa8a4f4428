import { Request, Response, NextFunction } from 'express';
import { UserStatus } from '@prisma/client';
import { prisma } from '@/config/database';
import { createError } from '@/middleware/errorHandler';
import { createNotification } from '@/services/notificationService';

/**
 * Reject user
 */
export const rejectUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        status: true,
      }
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    if (user.status !== UserStatus.PENDING) {
      throw createError('User is not pending approval', 400);
    }

    // Update user status
    const updatedUser = await prisma.user.update({
      where: { id },
      data: { status: UserStatus.REJECTED },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
      }
    });

    // Create notification
    await createNotification({
      userId: id,
      type: 'SYSTEM',
      title: 'Account Rejected',
      message: reason || 'Your account application has been rejected. Please contact support for more information.',
      data: { 
        rejectedBy: req.user?.userId,
        reason 
      }
    });

    res.json({
      message: 'User rejected successfully',
      user: updatedUser,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Suspend user
 */
export const suspendUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        status: true,
        role: true,
      }
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    if (user.role === 'ADMIN') {
      throw createError('Cannot suspend admin users', 403);
    }

    if (user.status === UserStatus.SUSPENDED) {
      throw createError('User is already suspended', 400);
    }

    // Update user status
    const updatedUser = await prisma.user.update({
      where: { id },
      data: { status: UserStatus.SUSPENDED },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
      }
    });

    // Create notification
    await createNotification({
      userId: id,
      type: 'SYSTEM',
      title: 'Account Suspended',
      message: reason || 'Your account has been suspended. Please contact support for more information.',
      data: { 
        suspendedBy: req.user?.userId,
        reason 
      }
    });

    res.json({
      message: 'User suspended successfully',
      user: updatedUser,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Activate user (unsuspend)
 */
export const activateUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        status: true,
      }
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    if (user.status !== UserStatus.SUSPENDED) {
      throw createError('User is not suspended', 400);
    }

    // Update user status
    const updatedUser = await prisma.user.update({
      where: { id },
      data: { status: UserStatus.APPROVED },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
      }
    });

    // Create notification
    await createNotification({
      userId: id,
      type: 'SYSTEM',
      title: 'Account Activated',
      message: 'Your account has been reactivated. You can now access all features of the Alumni Portal.',
      data: { activatedBy: req.user?.userId }
    });

    res.json({
      message: 'User activated successfully',
      user: updatedUser,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete user (soft delete by setting status to REJECTED)
 */
export const deleteUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
      }
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    if (user.role === 'ADMIN') {
      throw createError('Cannot delete admin users', 403);
    }

    if (user.id === req.user?.userId) {
      throw createError('Cannot delete your own account', 403);
    }

    // Soft delete by setting status to REJECTED
    await prisma.user.update({
      where: { id },
      data: { status: UserStatus.REJECTED }
    });

    res.json({
      message: 'User deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all jobs for admin review
 */
export const getAllJobs = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const [jobs, total] = await Promise.all([
      prisma.job.findMany({
        include: {
          postedBy: {
            select: {
              id: true,
              name: true,
              email: true,
              company: true,
            }
          },
          _count: {
            select: {
              applications: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.job.count()
    ]);

    res.json({
      jobs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all events for admin review
 */
export const getAllEvents = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const [events, total] = await Promise.all([
      prisma.event.findMany({
        include: {
          organizer: {
            select: {
              id: true,
              name: true,
              email: true,
              company: true,
            }
          },
          _count: {
            select: {
              rsvps: {
                where: { status: 'GOING' }
              }
            }
          }
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.event.count()
    ]);

    res.json({
      events,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
