apiVersion: v1
kind: Secret
metadata:
  name: alumni-portal-secrets
  namespace: alumni-portal
type: Opaque
stringData:
  # Plain text values - Kubernetes will encode them automatically
  DATABASE_URL: "********************************************/alumni_portal?schema=public&connection_limit=20&pool_timeout=20&socket_timeout=60"
  JWT_SECRET: "your-super-secret-jwt-key-here"
  JWT_REFRESH_SECRET: "your-refresh-token-secret-here"
  REDIS_PASSWORD: ""
  CLOUDINARY_API_SECRET: "your-api-secret"
  SMTP_PASS: "your-app-password"
