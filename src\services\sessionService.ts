import { redis } from "@/config/redis";
import { Logger } from "@/services/loggerService";
import { AuthUtils } from "@/utils/auth";
import crypto from "crypto";

// Session configuration
const SESSION_CONFIG = {
  // Session TTL in seconds
  sessionTTL: parseInt(process.env.SESSION_TTL || "86400"), // 24 hours

  // Refresh token TTL in seconds
  refreshTokenTTL: parseInt(process.env.REFRESH_TOKEN_TTL || "604800"), // 7 days

  // Maximum sessions per user
  maxSessionsPerUser: parseInt(process.env.MAX_SESSIONS_PER_USER || "5"),

  // Session cleanup interval
  cleanupInterval: parseInt(process.env.SESSION_CLEANUP_INTERVAL || "3600000"), // 1 hour

  // Enable session clustering
  enableClustering: process.env.ENABLE_SESSION_CLUSTERING !== "false",

  // Session key prefix
  sessionPrefix: process.env.SESSION_PREFIX || "session:",
  refreshPrefix: process.env.REFRESH_PREFIX || "refresh:",
  userSessionsPrefix: process.env.USER_SESSIONS_PREFIX || "user_sessions:",
};

// Session data interface
interface SessionData {
  userId: string;
  email: string;
  role: string;
  isApproved: boolean;
  createdAt: number;
  lastAccessedAt: number;
  ipAddress?: string;
  userAgent?: string;
  deviceId?: string;
  metadata?: Record<string, any>;
}

// Refresh token data interface
interface RefreshTokenData {
  userId: string;
  sessionId: string;
  createdAt: number;
  lastUsedAt: number;
  ipAddress?: string;
  userAgent?: string;
}

export class SessionService {
  // Initialize session service
  static init() {
    // Start periodic cleanup
    if (SESSION_CONFIG.enableClustering) {
      setInterval(() => {
        this.cleanupExpiredSessions();
      }, SESSION_CONFIG.cleanupInterval);

      Logger.info("Session service initialized with clustering enabled");
    } else {
      Logger.info("Session service initialized without clustering");
    }
  }

  // Create a new session
  static async createSession(
    userId: string,
    userData: Omit<SessionData, "userId" | "createdAt" | "lastAccessedAt">,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ sessionId: string; accessToken: string; refreshToken: string }> {
    try {
      const sessionId = this.generateSessionId();
      const refreshTokenId = this.generateRefreshTokenId();
      const now = Date.now();

      // Prepare session data
      const sessionData: SessionData = {
        userId,
        ...userData,
        createdAt: now,
        lastAccessedAt: now,
        ipAddress,
        userAgent,
      };

      // Prepare refresh token data
      const refreshTokenData: RefreshTokenData = {
        userId,
        sessionId,
        createdAt: now,
        lastUsedAt: now,
        ipAddress,
        userAgent,
      };

      // Check and enforce session limits
      await this.enforceSessionLimits(userId);

      // Store session and refresh token
      const sessionKey = `${SESSION_CONFIG.sessionPrefix}${sessionId}`;
      const refreshKey = `${SESSION_CONFIG.refreshPrefix}${refreshTokenId}`;
      const userSessionsKey = `${SESSION_CONFIG.userSessionsPrefix}${userId}`;

      await Promise.all([
        redis.setex(sessionKey, SESSION_CONFIG.sessionTTL, JSON.stringify(sessionData)),
        redis.setex(refreshKey, SESSION_CONFIG.refreshTokenTTL, JSON.stringify(refreshTokenData)),
        redis.sadd(userSessionsKey, sessionId),
        redis.expire(userSessionsKey, SESSION_CONFIG.refreshTokenTTL),
      ]);

      // Generate JWT tokens
      const accessToken = AuthUtils.generateSessionAccessToken({
        userId,
        email: userData.email,
        role: userData.role,
        sessionId,
      });

      const refreshToken = AuthUtils.generateSessionRefreshToken({
        userId,
        sessionId,
        refreshTokenId,
      });

      Logger.auth("Session created", userId, {
        sessionId,
        ipAddress,
        userAgent: userAgent?.substring(0, 100),
      });

      return {
        sessionId,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      Logger.error("Failed to create session", {
        userId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  // Get session data
  static async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      const sessionKey = `${SESSION_CONFIG.sessionPrefix}${sessionId}`;
      const sessionDataStr = await redis.get(sessionKey);

      if (!sessionDataStr) {
        return null;
      }

      const sessionData: SessionData = JSON.parse(sessionDataStr);

      // Update last accessed time
      sessionData.lastAccessedAt = Date.now();
      await redis.setex(sessionKey, SESSION_CONFIG.sessionTTL, JSON.stringify(sessionData));

      return sessionData;
    } catch (error) {
      Logger.error("Failed to get session", {
        sessionId,
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  // Refresh access token using refresh token
  static async refreshAccessToken(
    refreshToken: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ accessToken: string; refreshToken: string } | null> {
    try {
      // Verify refresh token
      const refreshPayload = AuthUtils.verifySessionRefreshToken(refreshToken);
      if (!refreshPayload) {
        return null;
      }

      const { userId, sessionId, refreshTokenId } = refreshPayload;

      // Get refresh token data
      const refreshKey = `${SESSION_CONFIG.refreshPrefix}${refreshTokenId}`;
      const refreshDataStr = await redis.get(refreshKey);

      if (!refreshDataStr) {
        Logger.warn("Refresh token not found in store", { refreshTokenId, userId });
        return null;
      }

      const refreshData: RefreshTokenData = JSON.parse(refreshDataStr);

      // Verify session still exists
      const sessionData = await this.getSession(sessionId);
      if (!sessionData) {
        Logger.warn("Session not found for refresh token", { sessionId, userId });
        await redis.del(refreshKey);
        return null;
      }

      // Generate new tokens
      const newRefreshTokenId = this.generateRefreshTokenId();
      const now = Date.now();

      // Update refresh token data
      const newRefreshData: RefreshTokenData = {
        ...refreshData,
        lastUsedAt: now,
        ipAddress,
        userAgent,
      };

      // Store new refresh token and remove old one
      const newRefreshKey = `${SESSION_CONFIG.refreshPrefix}${newRefreshTokenId}`;
      await Promise.all([
        redis.setex(newRefreshKey, SESSION_CONFIG.refreshTokenTTL, JSON.stringify(newRefreshData)),
        redis.del(refreshKey),
      ]);

      // Generate new JWT tokens
      const accessToken = AuthUtils.generateSessionAccessToken({
        userId: sessionData.userId,
        email: sessionData.email,
        role: sessionData.role,
        sessionId,
      });

      const newRefreshToken = AuthUtils.generateSessionRefreshToken({
        userId,
        sessionId,
        refreshTokenId: newRefreshTokenId,
      });

      Logger.auth("Access token refreshed", userId, {
        sessionId,
        oldRefreshTokenId: refreshTokenId,
        newRefreshTokenId,
      });

      return {
        accessToken,
        refreshToken: newRefreshToken,
      };
    } catch (error) {
      Logger.error("Failed to refresh access token", {
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  // Invalidate session
  static async invalidateSession(sessionId: string): Promise<boolean> {
    try {
      const sessionKey = `${SESSION_CONFIG.sessionPrefix}${sessionId}`;
      const sessionDataStr = await redis.get(sessionKey);

      if (!sessionDataStr) {
        return false;
      }

      const sessionData: SessionData = JSON.parse(sessionDataStr);
      const userSessionsKey = `${SESSION_CONFIG.userSessionsPrefix}${sessionData.userId}`;

      // Remove session from all stores
      await Promise.all([
        redis.del(sessionKey),
        redis.srem(userSessionsKey, sessionId),
        this.invalidateRefreshTokensForSession(sessionId),
      ]);

      Logger.auth("Session invalidated", sessionData.userId, { sessionId });
      return true;
    } catch (error) {
      Logger.error("Failed to invalidate session", {
        sessionId,
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  // Invalidate all sessions for a user
  static async invalidateAllUserSessions(userId: string): Promise<number> {
    try {
      const userSessionsKey = `${SESSION_CONFIG.userSessionsPrefix}${userId}`;
      const sessionIds = await redis.smembers(userSessionsKey);

      if (sessionIds.length === 0) {
        return 0;
      }

      // Remove all sessions
      const sessionKeys = sessionIds.map((id) => `${SESSION_CONFIG.sessionPrefix}${id}`);
      const invalidatePromises = [
        redis.del(...sessionKeys),
        redis.del(userSessionsKey),
        ...sessionIds.map((sessionId) => this.invalidateRefreshTokensForSession(sessionId)),
      ];

      await Promise.all(invalidatePromises);

      Logger.auth("All user sessions invalidated", userId, {
        sessionCount: sessionIds.length,
      });

      return sessionIds.length;
    } catch (error) {
      Logger.error("Failed to invalidate all user sessions", {
        userId,
        error: error instanceof Error ? error.message : String(error),
      });
      return 0;
    }
  }

  // Get all active sessions for a user
  static async getUserSessions(userId: string): Promise<SessionData[]> {
    try {
      const userSessionsKey = `${SESSION_CONFIG.userSessionsPrefix}${userId}`;
      const sessionIds = await redis.smembers(userSessionsKey);

      if (sessionIds.length === 0) {
        return [];
      }

      const sessionKeys = sessionIds.map((id) => `${SESSION_CONFIG.sessionPrefix}${id}`);
      const sessionDataStrings = await redis.mget(...sessionKeys);

      const sessions: SessionData[] = [];
      for (let i = 0; i < sessionDataStrings.length; i++) {
        if (sessionDataStrings[i]) {
          try {
            sessions.push(JSON.parse(sessionDataStrings[i]!));
          } catch (parseError) {
            Logger.error("Failed to parse session data", {
              sessionId: sessionIds[i],
              error: parseError,
            });
          }
        }
      }

      return sessions;
    } catch (error) {
      Logger.error("Failed to get user sessions", {
        userId,
        error: error instanceof Error ? error.message : String(error),
      });
      return [];
    }
  }

  // Enforce session limits per user
  private static async enforceSessionLimits(userId: string): Promise<void> {
    const userSessionsKey = `${SESSION_CONFIG.userSessionsPrefix}${userId}`;
    const sessionIds = await redis.smembers(userSessionsKey);

    if (sessionIds.length >= SESSION_CONFIG.maxSessionsPerUser) {
      // Remove oldest sessions
      const sessions = await this.getUserSessions(userId);
      const sortedSessions = sessions.sort((a, b) => a.lastAccessedAt - b.lastAccessedAt);

      const sessionsToRemove = sortedSessions.slice(0, sessionIds.length - SESSION_CONFIG.maxSessionsPerUser + 1);

      for (const session of sessionsToRemove) {
        const sessionKey = `${SESSION_CONFIG.sessionPrefix}${session.userId}`;
        await Promise.all([redis.del(sessionKey), redis.srem(userSessionsKey, session.userId)]);
      }

      Logger.auth("Session limit enforced", userId, {
        removedSessions: sessionsToRemove.length,
        maxSessions: SESSION_CONFIG.maxSessionsPerUser,
      });
    }
  }

  // Invalidate refresh tokens for a session
  private static async invalidateRefreshTokensForSession(sessionId: string): Promise<void> {
    try {
      // Find all refresh tokens for this session
      const pattern = `${SESSION_CONFIG.refreshPrefix}*`;
      const keys = await redis.keys(pattern);

      const refreshTokensToDelete: string[] = [];

      for (const key of keys) {
        const refreshDataStr = await redis.get(key);
        if (refreshDataStr) {
          try {
            const refreshData: RefreshTokenData = JSON.parse(refreshDataStr);
            if (refreshData.sessionId === sessionId) {
              refreshTokensToDelete.push(key);
            }
          } catch (parseError) {
            // Invalid refresh token data, delete it
            refreshTokensToDelete.push(key);
          }
        }
      }

      if (refreshTokensToDelete.length > 0) {
        await redis.del(...refreshTokensToDelete);
      }
    } catch (error) {
      Logger.error("Failed to invalidate refresh tokens for session", {
        sessionId,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  // Cleanup expired sessions
  private static async cleanupExpiredSessions(): Promise<void> {
    try {
      const pattern = `${SESSION_CONFIG.sessionPrefix}*`;
      const keys = await redis.keys(pattern);

      let cleanedSessions = 0;

      for (const key of keys) {
        const ttl = await redis.ttl(key);
        if (ttl === -1 || ttl === -2) {
          // Key has no TTL or doesn't exist, clean it up
          await redis.del(key);
          cleanedSessions++;
        }
      }

      if (cleanedSessions > 0) {
        Logger.info(`Cleaned up ${cleanedSessions} expired sessions`);
      }
    } catch (error) {
      Logger.error("Failed to cleanup expired sessions", error);
    }
  }

  // Generate unique session ID
  private static generateSessionId(): string {
    return crypto.randomBytes(32).toString("hex");
  }

  // Generate unique refresh token ID
  private static generateRefreshTokenId(): string {
    return crypto.randomBytes(32).toString("hex");
  }

  // Get session statistics
  static async getSessionStats(): Promise<{
    totalActiveSessions: number;
    totalActiveRefreshTokens: number;
    averageSessionsPerUser: number;
    oldestSession: number | null;
  }> {
    try {
      const [sessionKeys, refreshKeys] = await Promise.all([
        redis.keys(`${SESSION_CONFIG.sessionPrefix}*`),
        redis.keys(`${SESSION_CONFIG.refreshPrefix}*`),
      ]);

      let oldestSession: number | null = null;
      const userSessionCounts = new Map<string, number>();

      // Analyze sessions
      for (const key of sessionKeys) {
        const sessionDataStr = await redis.get(key);
        if (sessionDataStr) {
          try {
            const sessionData: SessionData = JSON.parse(sessionDataStr);
            userSessionCounts.set(sessionData.userId, (userSessionCounts.get(sessionData.userId) || 0) + 1);

            if (!oldestSession || sessionData.createdAt < oldestSession) {
              oldestSession = sessionData.createdAt;
            }
          } catch (parseError) {
            // Invalid session data
          }
        }
      }

      const averageSessionsPerUser =
        userSessionCounts.size > 0
          ? Array.from(userSessionCounts.values()).reduce((a, b) => a + b, 0) / userSessionCounts.size
          : 0;

      return {
        totalActiveSessions: sessionKeys.length,
        totalActiveRefreshTokens: refreshKeys.length,
        averageSessionsPerUser: Math.round(averageSessionsPerUser * 100) / 100,
        oldestSession,
      };
    } catch (error) {
      Logger.error("Failed to get session statistics", error);
      return {
        totalActiveSessions: 0,
        totalActiveRefreshTokens: 0,
        averageSessionsPerUser: 0,
        oldestSession: null,
      };
    }
  }
}

// Initialize session service
SessionService.init();
