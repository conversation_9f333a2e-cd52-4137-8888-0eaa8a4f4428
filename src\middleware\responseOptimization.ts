import { Request, Response, NextFunction } from "express";
import { Logger } from "@/services/loggerService";

// Response optimization configuration
const OPTIMIZATION_CONFIG = {
  // Enable response minification for JSON
  minifyJson: process.env.MINIFY_JSON !== "false",
  
  // Enable field filtering
  enableFieldFiltering: process.env.ENABLE_FIELD_FILTERING !== "false",
  
  // Enable response transformation
  enableTransformation: process.env.ENABLE_TRANSFORMATION !== "false",
  
  // Maximum response size before warning (in bytes)
  maxResponseSize: parseInt(process.env.MAX_RESPONSE_SIZE || "10485760"), // 10MB
  
  // Enable response caching headers
  enableCacheHeaders: process.env.ENABLE_CACHE_HEADERS !== "false",
};

// Field filtering middleware
export const fieldFiltering = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!OPTIMIZATION_CONFIG.enableFieldFiltering) {
      return next();
    }

    const fields = req.query.fields as string;
    const exclude = req.query.exclude as string;

    if (!fields && !exclude) {
      return next();
    }

    // Store original json method
    const originalJson = res.json.bind(res);

    // Override json method to apply field filtering
    res.json = function(data: any) {
      try {
        const filteredData = applyFieldFiltering(data, fields, exclude);
        return originalJson(filteredData);
      } catch (error) {
        Logger.error("Field filtering failed", {
          error: error instanceof Error ? error.message : String(error),
          fields,
          exclude,
          path: req.path,
        });
        return originalJson(data); // Fallback to original data
      }
    };

    next();
  };
};

// Apply field filtering to data
function applyFieldFiltering(data: any, fields?: string, exclude?: string): any {
  if (!data || typeof data !== "object") {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(item => applyFieldFiltering(item, fields, exclude));
  }

  let result = { ...data };

  // Include only specified fields
  if (fields) {
    const includeFields = fields.split(",").map(f => f.trim());
    const filteredResult: any = {};
    
    for (const field of includeFields) {
      if (field.includes(".")) {
        // Handle nested fields (e.g., "user.name")
        const [parent, ...nested] = field.split(".");
        if (result[parent]) {
          if (!filteredResult[parent]) {
            filteredResult[parent] = {};
          }
          const nestedField = nested.join(".");
          filteredResult[parent] = {
            ...filteredResult[parent],
            ...applyFieldFiltering(result[parent], nestedField),
          };
        }
      } else if (result.hasOwnProperty(field)) {
        filteredResult[field] = result[field];
      }
    }
    
    result = filteredResult;
  }

  // Exclude specified fields
  if (exclude) {
    const excludeFields = exclude.split(",").map(f => f.trim());
    
    for (const field of excludeFields) {
      if (field.includes(".")) {
        // Handle nested fields
        const [parent, ...nested] = field.split(".");
        if (result[parent]) {
          const nestedField = nested.join(".");
          result[parent] = applyFieldFiltering(result[parent], undefined, nestedField);
        }
      } else {
        delete result[field];
      }
    }
  }

  return result;
}

// Response compression optimization
export const responseCompression = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Store original json method
    const originalJson = res.json.bind(res);

    res.json = function(data: any) {
      try {
        let processedData = data;

        // Minify JSON if enabled
        if (OPTIMIZATION_CONFIG.minifyJson) {
          // Remove null/undefined values and empty objects/arrays
          processedData = removeEmptyValues(processedData);
        }

        // Check response size
        const responseSize = JSON.stringify(processedData).length;
        if (responseSize > OPTIMIZATION_CONFIG.maxResponseSize) {
          Logger.warn("Large response detected", {
            size: `${Math.round(responseSize / 1024 / 1024)}MB`,
            path: req.path,
            method: req.method,
          });
        }

        return originalJson(processedData);
      } catch (error) {
        Logger.error("Response compression failed", {
          error: error instanceof Error ? error.message : String(error),
          path: req.path,
        });
        return originalJson(data);
      }
    };

    next();
  };
};

// Remove empty values from response
function removeEmptyValues(obj: any): any {
  if (obj === null || obj === undefined) {
    return undefined;
  }

  if (Array.isArray(obj)) {
    const filtered = obj
      .map(item => removeEmptyValues(item))
      .filter(item => item !== undefined);
    return filtered.length > 0 ? filtered : undefined;
  }

  if (typeof obj === "object") {
    const cleaned: any = {};
    let hasValues = false;

    for (const [key, value] of Object.entries(obj)) {
      const cleanedValue = removeEmptyValues(value);
      if (cleanedValue !== undefined) {
        cleaned[key] = cleanedValue;
        hasValues = true;
      }
    }

    return hasValues ? cleaned : undefined;
  }

  return obj;
}

// Response transformation middleware
export const responseTransformation = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!OPTIMIZATION_CONFIG.enableTransformation) {
      return next();
    }

    const transform = req.query.transform as string;
    
    if (!transform) {
      return next();
    }

    // Store original json method
    const originalJson = res.json.bind(res);

    res.json = function(data: any) {
      try {
        const transformedData = applyTransformation(data, transform);
        return originalJson(transformedData);
      } catch (error) {
        Logger.error("Response transformation failed", {
          error: error instanceof Error ? error.message : String(error),
          transform,
          path: req.path,
        });
        return originalJson(data);
      }
    };

    next();
  };
};

// Apply transformation to response data
function applyTransformation(data: any, transform: string): any {
  switch (transform.toLowerCase()) {
    case "flatten":
      return flattenObject(data);
    
    case "camelcase":
      return convertKeysToCamelCase(data);
    
    case "snakecase":
      return convertKeysToSnakeCase(data);
    
    case "compact":
      return removeEmptyValues(data);
    
    default:
      Logger.warn(`Unknown transformation: ${transform}`);
      return data;
  }
}

// Flatten nested objects
function flattenObject(obj: any, prefix = ""): any {
  if (!obj || typeof obj !== "object" || Array.isArray(obj)) {
    return obj;
  }

  const flattened: any = {};

  for (const [key, value] of Object.entries(obj)) {
    const newKey = prefix ? `${prefix}.${key}` : key;

    if (value && typeof value === "object" && !Array.isArray(value)) {
      Object.assign(flattened, flattenObject(value, newKey));
    } else {
      flattened[newKey] = value;
    }
  }

  return flattened;
}

// Convert keys to camelCase
function convertKeysToCamelCase(obj: any): any {
  if (!obj || typeof obj !== "object") {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }

  const converted: any = {};
  for (const [key, value] of Object.entries(obj)) {
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    converted[camelKey] = convertKeysToCamelCase(value);
  }

  return converted;
}

// Convert keys to snake_case
function convertKeysToSnakeCase(obj: any): any {
  if (!obj || typeof obj !== "object") {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToSnakeCase(item));
  }

  const converted: any = {};
  for (const [key, value] of Object.entries(obj)) {
    const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    converted[snakeKey] = convertKeysToSnakeCase(value);
  }

  return converted;
}

// Cache headers middleware
export const cacheHeaders = (options: {
  maxAge?: number;
  private?: boolean;
  noCache?: boolean;
  mustRevalidate?: boolean;
} = {}) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!OPTIMIZATION_CONFIG.enableCacheHeaders) {
      return next();
    }

    const {
      maxAge = 300, // 5 minutes default
      private: isPrivate = false,
      noCache = false,
      mustRevalidate = false,
    } = options;

    // Don't cache error responses
    const originalStatus = res.status.bind(res);
    res.status = function(code: number) {
      if (code >= 400) {
        res.set("Cache-Control", "no-cache, no-store, must-revalidate");
        res.set("Pragma", "no-cache");
        res.set("Expires", "0");
      }
      return originalStatus(code);
    };

    // Set cache headers for successful responses
    if (req.method === "GET") {
      let cacheControl = [];

      if (noCache) {
        cacheControl.push("no-cache");
      } else {
        if (isPrivate) {
          cacheControl.push("private");
        } else {
          cacheControl.push("public");
        }
        cacheControl.push(`max-age=${maxAge}`);
      }

      if (mustRevalidate) {
        cacheControl.push("must-revalidate");
      }

      res.set("Cache-Control", cacheControl.join(", "));
      
      // Set ETag for conditional requests
      const originalJson = res.json.bind(res);
      res.json = function(data: any) {
        const etag = generateETag(data);
        res.set("ETag", etag);
        
        // Check if client has cached version
        if (req.get("If-None-Match") === etag) {
          return res.status(304).end();
        }
        
        return originalJson(data);
      };
    }

    next();
  };
};

// Generate ETag for response data
function generateETag(data: any): string {
  const crypto = require("crypto");
  const content = JSON.stringify(data);
  return `"${crypto.createHash("md5").update(content).digest("hex")}"`;
}

// Response pagination optimization
export const paginationOptimization = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100); // Max 100 items
    const offset = (page - 1) * limit;

    // Add pagination info to request
    req.pagination = {
      page,
      limit,
      offset,
    };

    // Store original json method
    const originalJson = res.json.bind(res);

    res.json = function(data: any) {
      // If data has pagination info, format it properly
      if (data && typeof data === "object" && data.items && data.total !== undefined) {
        const totalPages = Math.ceil(data.total / limit);
        
        const paginatedResponse = {
          data: data.items,
          pagination: {
            page,
            limit,
            total: data.total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
            nextPage: page < totalPages ? page + 1 : null,
            prevPage: page > 1 ? page - 1 : null,
          },
        };

        // Add pagination links
        const baseUrl = `${req.protocol}://${req.get("host")}${req.path}`;
        const queryParams = new URLSearchParams(req.query as any);
        
        if (page < totalPages) {
          queryParams.set("page", (page + 1).toString());
          paginatedResponse.pagination.nextUrl = `${baseUrl}?${queryParams}`;
        }
        
        if (page > 1) {
          queryParams.set("page", (page - 1).toString());
          paginatedResponse.pagination.prevUrl = `${baseUrl}?${queryParams}`;
        }

        return originalJson(paginatedResponse);
      }

      return originalJson(data);
    };

    next();
  };
};

// Declare pagination on Request interface
declare global {
  namespace Express {
    interface Request {
      pagination?: {
        page: number;
        limit: number;
        offset: number;
      };
    }
  }
}
